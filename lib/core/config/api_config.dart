// Copyright (c) 2025 Almas Inc All rights reserved.
// 修改时间: 2025/02/06 16:32
// 作者: Elshat
// 文件: api_config.dart
// 描述: API配置文件

import 'dart:io';

import 'package:package_info_plus/package_info_plus.dart';
import 'package:user_app/core/config/environment_config.dart';

/// API配置类
///
/// 提供API相关的配置信息，如基础URL、超时设置、请求头等
class ApiConfig {
  // 私有构造函数，防止实例化
  ApiConfig._();

  /// 缓存的包信息
  static PackageInfo? _packageInfo;

  /// API基础URL
  ///
  /// 从环境配置中获取当前环境的API基础URL
  static String get baseUrl => EnvironmentConfig.apiBaseUrl;

  /// 连接超时设置（毫秒）
  static int get connectTimeout => EnvironmentConfig.connectTimeout;

  /// 接收超时设置（毫秒）
  static int get receiveTimeout => EnvironmentConfig.receiveTimeout;

  /// 获取应用版本号
  static Future<String> getAppVersion() async {
    _packageInfo ??= await PackageInfo.fromPlatform();
    return 'v${_packageInfo!.version}';
  }

  /// 获取同步版本号（如果已经初始化）
  static String getAppVersionSync() {
    return _packageInfo != null
        ? '${_packageInfo!.version}(${_packageInfo!.buildNumber})'
        : 'v3.1.3';
  }

  /// 初始化包信息（建议在应用启动时调用）
  static Future<void> initPackageInfo() async {
    _packageInfo = await PackageInfo.fromPlatform();
  }

  /// 获取默认请求头
  static Map<String, dynamic> getDefaultHeaders() {
    return {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
      'searialNumber': '123456789',
      "platform": "app",
      'terminalId': Platform.isAndroid ? 9 : 10,
      'parentCategoryId': 1,
      'mini-version': getAppVersionSync(),
      'supportRealTimeOrder': 1,
    };
  }

  /// 默认请求头（保持向后兼容）
  static Map<String, dynamic> get defaultHeaders => getDefaultHeaders();

  /// 重试配置 - 最大重试次数
  static int get maxRetries => EnvironmentConfig.maxRetries;

  /// 重试配置 - 重试延迟时间（毫秒）
  static int get retryDelay => EnvironmentConfig.retryDelay;

  /// WebSocket URL
  ///
  /// 从环境配置中获取当前环境的WebSocket URL
  static String get socketUrl => EnvironmentConfig.socketUrl;
}
