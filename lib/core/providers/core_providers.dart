import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'package:user_app/core/network/api_client.dart';
import 'package:user_app/core/storage/storage_service.dart';
import 'package:user_app/data/models/location/location_model.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';

part 'core_providers.g.dart';

/// API客户端提供者
@Riverpod(keepAlive: true)
ApiClient apiClient(final Ref ref) {
  return ApiClient();
}

/// 存储服务提供者
@Riverpod(keepAlive: true)
StorageService storageService(final Ref ref) {
  return StorageService();
}

// 保留languageProvider作为全局设置，其他状态将通过HomeState管理
final languageProvider = StateProvider<String>((ref) {
  return ref.read(localStorageRepositoryProvider).getLang() ?? "ug";
});

/// 主页面当前选中的标签索引
///
/// 使用全局状态管理主页面的标签索引，避免路由跳转时的重建问题
/// 初始值为0，对应首页标签
final mainTabIndexProvider = StateProvider<int>((final ref) => 0);

final locationInfoProvider = StateProvider<LocationModel>((final ref) {
  return LocationModel(
      latitude: "0.0",
      longitude: "0.0",
      accuracy: "0.0",
      address: "",
      speed: "0.0",
      description: "",
      errorCode: 0);
});

/// 特价活动ID提供者
/// 用于存储当前特价美食的活动ID，在订单提交时需要传递给后端
final specialIdProvider = StateProvider<int?>((final ref) => null);
