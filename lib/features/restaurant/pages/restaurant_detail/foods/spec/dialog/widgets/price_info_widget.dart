import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_controller.dart';
import 'package:user_app/generated/l10n.dart';

/// 价格信息组件
class PriceInfoWidget extends ConsumerWidget {
  const PriceInfoWidget({super.key});

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 监听价格相关状态
    final currentPrice = ref.watch(
      specModalControllerProvider.select((final state) => state.currentPrice),
    );
    final basePrice = ref.watch(
      specModalControllerProvider.select((final state) => state.basePrice),
    );
    final additionalPrice = ref.watch(
      specModalControllerProvider
          .select((final state) => state.additionalPrice),
    );
    final hasPreferential = ref.watch(
      specModalControllerProvider
          .select((final state) => state.hasPreferential),
    );
    final hasSeckill = ref.watch(
      specModalControllerProvider.select((final state) => state.hasSeckill),
    );
    final maxOrderCount = ref.watch(
      specModalControllerProvider.select((final state) => state.maxOrderCount),
    );
    final seckillMaxOrderCount = ref.watch(
      specModalControllerProvider
          .select((final state) => state.seckillMaxOrderCount),
    );

    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 当前价格
              Row(
                children: [
                  Text(
                    FormatUtil.formatPrice(currentPrice),
                    style: TextStyle(
                      fontSize: 31.sp, // 62rpx / 2
                      fontFamily: AppConstants.numberFont,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                      height: 0.6,
                    ),
                  ),
                  Text(
                    '¥',
                    style: TextStyle(
                      fontSize: 18.sp, // 36rpx / 2
                      fontWeight: FontWeight.bold,
                      fontFamily: AppConstants.numberFont,
                      color: AppColors.primary,
                    ),
                  ),

                  // 原价（如果不同）
                  if ((basePrice + additionalPrice) != currentPrice &&
                      (basePrice + additionalPrice) > 0) ...[
                    SizedBox(width: 8.w),
                    Row(
                      children: [
                        Text(
                          FormatUtil.formatPrice(basePrice + additionalPrice),
                          style: TextStyle(
                            fontSize: 15.sp, // 30rpx / 2
                            color: Color(0xFF8D8C8C),
                            decoration: TextDecoration.lineThrough,
                          ),
                        ),
                        Text(
                          '¥',
                          style: TextStyle(
                            fontSize: 15.sp, // 30rpx / 2
                            color: Color(0xFF8D8C8C),
                            decoration: TextDecoration.lineThrough,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),

              // 活动信息
              if (hasPreferential || hasSeckill) ...[
                SizedBox(height: 4.h),
                Text(
                  hasSeckill
                      ? '${S.current.seckill_total_count}: $seckillMaxOrderCount'
                      : '${S.current.discount_max_count}: $maxOrderCount',
                  style: TextStyle(
                    fontSize: 14.sp, // 28rpx / 2
                    color: Color(0xFF8D8C8C),
                  ),
                  textAlign: TextAlign.start,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
