import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 食品图片组件
class FoodImageWidget extends StatelessWidget {
  final String imageUrl;
  final bool isMainFood;
  final int? style;
  final VoidCallback? onTap;

  const FoodImageWidget({
    super.key,
    required this.imageUrl,
    this.isMainFood = false,
    this.style,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // 根据样式调整图片尺寸
    // 微信小程序中的图片尺寸约为180-200rpx，按照2:1比例转换为Flutter
    // 样式2和样式3的图片更大一些，样式1的稍小
    final double size = isMainFood ? 95.w : 75.w; // 其他样式 (180rpx -> 90.w)

    // 根据样式选择不同的边框圆角
    final BorderRadius borderRadius = _getImageBorderRadius(context);

    // 根据样式决定图片的边距
    // 只有餐厅样式(样式1)的主图有内边距，样式2和3没有
    final EdgeInsetsGeometry padding = isMainFood && style == 1
        ? EdgeInsets.all(8.w) // 16rpx -> 8.w
        : EdgeInsets.zero;

    Widget imageWidget = Padding(
      padding: padding,
      child: ClipRRect(
        borderRadius: borderRadius,
        child: CachedNetworkImage(
          imageUrl: imageUrl,
          width: size,
          height: size,
          fadeInDuration: Duration(milliseconds: 100),
          memCacheHeight: isMainFood ? 200 : null,
          memCacheWidth: isMainFood ? 200 : null,
          // placeholder: isMainFood
          //     ? (final context, final url) {
          //         return Container(
          //           width: size,
          //           height: size,
          //           decoration: BoxDecoration(
          //             color: Colors.grey[200],
          //             borderRadius: BorderRadius.circular(8.r),
          //           ),
          //         );
          //       }
          //     : null,
          fit: BoxFit.cover,
          // errorWidget: (final context, final url, final error) => Container(
          //   width: size,
          //   height: size,
          //   color: Colors.grey[300],
          //   child: Icon(
          //     Icons.restaurant_menu,
          //     color: Colors.grey[400],
          //     size: size,
          //   ),
          // ),
        ),
      ),
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// 根据样式获取图片边框圆角
  BorderRadius _getImageBorderRadius(BuildContext context) {
    // 获取当前文本方向
    final textDirection = Directionality.of(context);
    final bool isRTL = textDirection == TextDirection.rtl;

    // 为所有非主食品或默认样式返回完整圆角
    if (!isMainFood || (style != 2 && style != 3)) {
      return BorderRadius.circular(8.r);
    }

    // 对于样式2和样式3的主食品，根据文本方向设置圆角
    if (isRTL) {
      // 从右到左方向 (RTL) - 维吾尔语
      return BorderRadius.only(
        topRight: Radius.circular(8.r),
        bottomRight: Radius.circular(8.r),
      );
    } else {
      // 从左到右方向 (LTR) - 中文
      return BorderRadius.only(
        topLeft: Radius.circular(8.r),
        bottomLeft: Radius.circular(8.r),
      );
    }
  }
}
