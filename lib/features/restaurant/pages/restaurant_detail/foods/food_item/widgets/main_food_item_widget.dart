import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/food_image_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/sales_quantity_info_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/price_quantity_row_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/seckill_info_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/multi_discount_tag_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/reduction_tags_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/combo_food_widget.dart';

/// 主食品项组件
class MainFoodItemWidget extends ConsumerWidget {
  final int? style;
  final Food? foodItem;
  final int foodCount;
  final bool relationsExists;
  final List<dynamic>? fromFoodIds;
  final String hour;
  final String minute;
  final String second;
  final bool? isResting;
  final VoidCallback? onTap;
  final Function(int, int?, Food?, bool, [BuildContext?])? onAdd;
  final Function(int?)? onRemove;
  final Function(Food)? onAddMinCount;
  final Function(Food)? onSpecSelect;

  const MainFoodItemWidget({
    super.key,
    this.style,
    this.foodItem,
    required this.foodCount,
    required this.relationsExists,
    this.fromFoodIds,
    required this.hour,
    required this.minute,
    required this.second,
    this.isResting,
    this.onTap,
    this.onAdd,
    this.onRemove,
    this.onAddMinCount,
    this.onSpecSelect,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 启用多份打折功能
    final bool hasMultiDiscount = foodItem?.multiDiscountId != null &&
        (foodItem?.multiDiscountSteps?.isNotEmpty ?? false);

    // 检查是否是套餐商品
    final bool isComboFood = foodItem?.foodType == 2 &&
        foodItem?.comboFoodItems != null &&
        foodItem!.comboFoodItems!.isNotEmpty;

    // 高亮显示处理
    final bool isHighLight = fromFoodIds?.contains(foodItem?.id) ?? false;
    final Color isHighLightColor =
        isHighLight ? Color(0xFFbcf0d7) : Colors.white;

    // 根据样式选择不同的布局
    if (style == 2 || style == 3) {
      // 超市便利样式和便利店样式 - 水平布局，封面图片无外边距
      return Container(
        decoration: BoxDecoration(
          color: isHighLightColor,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Stack(
          children: [
            // 多折扣标签
            if (hasMultiDiscount) MultiDiscountTagWidget(foodItem: foodItem),
            Column(
              children: [
                // 如果是套餐商品，显示套餐组件，否则显示普通主要内容行
                if (isComboFood)
                  ComboFoodWidget(
                    foodItem: foodItem!,
                    style: style,
                    isResting: isResting,
                    foodCount: foodCount,
                    onTap: onTap,
                    onAdd: onAdd,
                    onRemove: onRemove,
                    onAddMinCount: onAddMinCount,
                    onSpecSelect: onSpecSelect,
                  )
                else
                  // 主要内容行（图片+信息）
                  _buildMainContentRow(hasMultiDiscount, isHighLight),
                // 秒杀信息.离开始还剩
                SeckillInfoWidget(
                  foodItem: foodItem,
                  hour: hour,
                  minute: minute,
                  second: second,
                  isComboFood: isComboFood,
                ),
                // 满减标签 - 与微信小程序foodCard.wxml逻辑一致
                ReductionTagsWidget(
                  foodItem: foodItem,
                ),
              ],
            ),
          ],
        ),
      );
    } else {
      // 餐厅样式 - 原有的布局，封面图片有内边距
      return Container(
        decoration: BoxDecoration(
          color: isHighLightColor,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Stack(
          children: [
            // 多折扣标签
            if (hasMultiDiscount) MultiDiscountTagWidget(foodItem: foodItem),
            Column(
              children: [
                // 如果是套餐商品，显示套餐组件，否则显示普通主要内容行
                if (isComboFood)
                  ComboFoodWidget(
                    foodItem: foodItem!,
                    style: style,
                    isResting: isResting,
                    foodCount: foodCount,
                    onTap: onTap,
                    onAdd: onAdd,
                    onRemove: onRemove,
                    onAddMinCount: onAddMinCount,
                    onSpecSelect: onSpecSelect,
                  )
                else
                  // 主要内容行（图片+信息）
                  _buildMainContentRow(hasMultiDiscount, isHighLight),
                // 秒杀信息.离开始还剩
                SeckillInfoWidget(
                  foodItem: foodItem,
                  hour: hour,
                  minute: minute,
                  second: second,
                  isComboFood: isComboFood,
                ),
                // 满减标签 - 与微信小程序foodCard.wxml逻辑一致
                ReductionTagsWidget(
                  foodItem: foodItem,
                ),
              ],
            ),
          ],
        ),
      );
    }
  }

  /// 构建主要内容行（图片+食品信息）
  /// 从多折扣标签下面分离出来的Row部分
  Widget _buildMainContentRow(
      final bool hasMultiDiscount, final bool isHighLight) {
    if (style == 2 || style == 3) {
      // 超市便利样式和便利店样式
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 食品图片 - 无外边距
          FoodImageWidget(
            imageUrl: foodItem?.image ?? "",
            isMainFood: true,
            style: style,
            onTap: onTap,
          ),
          SizedBox(width: 5.w),
          // 食品信息
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(top: 5.h, right: 0.w, bottom: 0.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 食品名称 - 考虑多折扣标签占用空间
                  SizedBox(
                    width: hasMultiDiscount
                        ? double.infinity - 25.w
                        : // 减去标签空间
                        double.infinity,
                    child: Text(
                      foodItem?.name ?? "",
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  // 销量信息
                  SalesQuantityInfoWidget(foodItem: foodItem),
                  SizedBox(height: 4.h),
                  // 价格和数量控制
                  PriceQuantityRowWidget(
                    price: foodItem?.price,
                    foodCount: foodCount,
                    foodId: foodItem?.id,
                    food: foodItem,
                    relationsExists: relationsExists,
                    fontSize: 20.sp,
                    isResting: isResting,
                    onAdd: onAdd,
                    onRemove: onRemove,
                    onAddMinCount: onAddMinCount,
                    onSpecSelect: onSpecSelect,
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    } else {
      // 餐厅样式
      return Row(
        children: [
          FoodImageWidget(
            imageUrl: foodItem?.image ?? "",
            isMainFood: true,
            style: style,
            onTap: onTap,
          ),
          SizedBox(width: 5.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 食品名称 - 考虑多折扣标签占用空间
                SizedBox(
                  width: hasMultiDiscount
                      ? double.infinity - 25.w
                      : // 减去标签空间
                      double.infinity,
                  child: Text(
                    foodItem?.name ?? "",
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(height: 4.h),
                SalesQuantityInfoWidget(foodItem: foodItem),
                PriceQuantityRowWidget(
                  price: foodItem?.price,
                  foodCount: foodCount,
                  foodId: foodItem?.id,
                  food: foodItem,
                  relationsExists: relationsExists,
                  fontSize: 16.sp,
                  isResting: isResting,
                  onAdd: onAdd,
                  onRemove: onRemove,
                  onAddMinCount: onAddMinCount,
                  onSpecSelect: onSpecSelect,
                ),
              ],
            ),
          ),
        ],
      );
    }
  }
}
