import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/generated/l10n.dart';

/// 倒计时状态Provider
final countdownProvider = StateNotifierProvider.autoDispose
    .family<CountdownNotifier, int, List<Food?>>(
  (final ref, final foods) {
    return CountdownNotifier(foods);
  },
);

/// 倒计时状态管理
class CountdownNotifier extends StateNotifier<int> {
  Timer? _timer;

  CountdownNotifier(final List<Food?> foods) : super(0) {
    _initCountdown(foods);
  }

  /// 初始化倒计时
  void _initCountdown(final List<Food?> foods) {
    List<int> seckillTimes = [];
    for (int i = 0; i < foods.length; i++) {
      if ((foods[i]?.seckillTime ?? 0) != 0) {
        seckillTimes.add(foods[i]!.seckillTime!);
      }
    }
    int minTime = seckillTimes.isNotEmpty ? seckillTimes.reduce(min) : 0;
    state = minTime;

    if (minTime > 0) {
      startCountdown();
    }
  }

  /// 开始倒计时
  void startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (final _) {
      if (state > 0) {
        state--;
      } else {
        _timer?.cancel();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
}

/// 餐厅秒杀倒计时组件
///
/// 用于显示餐厅秒杀商品的倒计时
/// 使用单独的状态管理器，避免重建整个页面
class RestaurantCountdownWidget extends ConsumerWidget {
  /// 食品列表数据
  final List<Food?>? foods;

  final double? top;

  /// 构造函数
  ///
  /// @param key Widget的key
  /// @param foods 食品列表数据
  const RestaurantCountdownWidget({
    super.key,
    required this.foods,
    this.top,
  });

  @override
  Widget build(final BuildContext context, final WidgetRef ref) {
    // 使用select只监听倒计时状态，避免不必要的重建
    final restSecond = ref.watch(countdownProvider(foods ?? []));

    // 如果倒计时为0，不显示组件
    if (restSecond <= 0) {
      return const SizedBox.shrink();
    }

    // 转换时间格式
    final timeMap = _convertSeconds(restSecond);

    return Container(
      margin: EdgeInsets.only(
        left: 8.w,
        right: 8.w,
        top: top ?? 0,
      ),
      height: 36.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6.w),
        color: AppColors.baseGreenColor,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(width: 10.w),
          // 根据当前语言环境判断是RTL还是LTR，并相应调整组件顺序
          // 获取当前的文本方向
          Builder(
            builder: (final context) {
              final TextDirection currentDirection = Directionality.of(context);

              // 创建时间组件列表
              List<Widget> timeWidgets = [];
              if (currentDirection == TextDirection.ltr) {
                // LTR布局 - 时:分:秒
                timeWidgets = [
                  _timeWidget(timeMap['hours'] ?? '00'),
                  Container(
                    alignment: Alignment.center,
                    width: 15.w,
                    child: Text(
                      ':',
                      style: TextStyle(
                        fontSize: titleSize,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _timeWidget(timeMap['minutes'] ?? '00'),
                  Container(
                    alignment: Alignment.center,
                    width: 15.w,
                    child: Text(
                      ':',
                      style: TextStyle(
                        fontSize: titleSize,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _timeWidget(timeMap['seconds'] ?? '00'),
                ];
              } else {
                // RTL布局 - 秒:分:时
                timeWidgets = [
                  _timeWidget(timeMap['seconds'] ?? '00'),
                  Container(
                    alignment: Alignment.center,
                    width: 15.w,
                    child: Text(
                      ':',
                      style: TextStyle(
                        fontSize: titleSize,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _timeWidget(timeMap['minutes'] ?? '00'),
                  Container(
                    alignment: Alignment.center,
                    width: 15.w,
                    child: Text(
                      ':',
                      style: TextStyle(
                        fontSize: titleSize,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _timeWidget(timeMap['hours'] ?? '00'),
                ];
              }

              return Row(children: timeWidgets);
            },
          ),
          SizedBox(width: 8.w),
          Text(
            S.current.in_end_out_sec,
            style: TextStyle(
              fontSize: mainSize,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// 倒计时显示组件
  Widget _timeWidget(final String timeVal) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.w),
        color: Colors.white,
      ),
      child: Text(
        timeVal,
        style: TextStyle(
          fontSize: secondSize,
          color: Colors.orange,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 将秒数转换为时:分:秒格式
  Map<String, String> _convertSeconds(final int totalSeconds) {
    int hours = totalSeconds ~/ 3600; // 计算小时
    int minutes = (totalSeconds % 3600) ~/ 60; // 计算分钟
    int seconds = totalSeconds % 60; // 计算剩余的秒数

    // 格式化为 2 位数，小时、分钟和秒数都小于 10 时前面补零
    String formattedHours = hours.toString().padLeft(2, '0');
    String formattedMinutes = minutes.toString().padLeft(2, '0');
    String formattedSeconds = seconds.toString().padLeft(2, '0');

    return {
      'hours': formattedHours,
      'minutes': formattedMinutes,
      'seconds': formattedSeconds,
    };
  }
}
