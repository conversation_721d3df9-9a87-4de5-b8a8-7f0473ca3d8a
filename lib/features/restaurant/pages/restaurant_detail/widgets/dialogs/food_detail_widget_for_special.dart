import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';

import 'package:user_app/core/utils/image_util.dart';
import 'package:user_app/core/utils/wechat_util.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/core/widgets/image_viewer.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/widgets/prefect_image_width.dart';
import 'package:user_app/core/widgets/star_rating.dart';
import 'package:user_app/data/models/activity/special_food_model.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/data/models/restaurant/restaurant_comment_list_model.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/widgets/quantity_control_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/widgets/restaurant_bottom_bar_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/restaurant_detail_controller.dart';
import 'package:user_app/features/restaurant/providers/food_detail_provider.dart';
import 'package:user_app/features/restaurant/providers/shopping_cart_provider.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/food_item/restaurant_foods_item.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/foods/spec/dialog/spec_modal_widget.dart';
import 'package:user_app/generated/l10n.dart';

/// 食品详情页组件
/// 用于显示食品的详细信息，包括图片、名称、价格、描述、评论等
class FoodDetailWidgetForSpecial extends ConsumerStatefulWidget {
  /// 构造函数
  /// @param monthOrderCount 月销量
  /// @param seckillMaxOrderCount 秒杀限购数量
  /// @param foodQuantityTypeValUg 食品数量类型值（乌兹别克语）
  /// @param foodQuantityTypeValZh 食品数量类型值（中文）
  /// @param seckillId 秒杀活动ID
  /// @param specialId 特价活动ID
  FoodDetailWidgetForSpecial({
    super.key,
    required this.restaurantId,
    required this.food,
    required this.distribution,
    this.specialId,
  });
  int restaurantId;
  SpecialItems? food;
  SpecialDistribution? distribution;
  int? specialId;

  @override
  ConsumerState createState() => _FoodDetailWidgetState();
}

class _FoodDetailWidgetState extends ConsumerState<FoodDetailWidgetForSpecial> {
  int page = 1;
  int type = 1;
  int id = 0;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((final _) {
      // 只有在组件仍然mounted时才执行初始化
      if (mounted) {
        // 创建食品对象
        Food food = Food(
          id: widget.food?.foodId ?? 0,
          image: widget.food?.image,
          price: widget.food?.price ?? 0,
          originPrice: widget.food?.oldPrice ?? 0,
          oldPrice: widget.food?.oldPrice ?? 0,
          maxOrderCount: widget.food?.maxOrderCount ?? 0,
          state: widget.food?.state ?? 0,
          lunchBoxFee: widget.food?.lunchBoxFee ?? 0,
          lunchBoxId: widget.food?.lunchBoxId ?? 0,
          specialActive: 1,
          percent: widget.food?.percent ?? 0,
          seckillId: widget.food?.seckillId ?? 0,
          seckillActive: widget.food?.seckillActive ?? 0,
          originProfit: widget.food?.originalProfit ?? 0,
          profit: widget.food?.profit ?? 0,
          foodType: widget.food?.foodType ?? 0,
        );

        // 创建FoodsData对象并更新到控制器状态中
        final foodsData = FoodsData(
          distribution: Distribution(
            distance: widget.distribution?.distance ?? 0,
            param: widget.distribution?.param ?? 0,
            shipment: widget.distribution?.shipment ?? 0,
          ),
          foods: [food],
        );

        // 使用控制器的updateFoodsData方法更新状态
        ref
            .read(restaurantDetailControllerProvider.notifier)
            .updateFoodsData(foodsData);

        // 设置特价活动ID到全局状态
        if (widget.specialId != null) {
          ref.read(specialIdProvider.notifier).state = widget.specialId;
        }

        // 添加到购物车
        _handleAddFood(0, food.id, food, false);
      }
    });
  }

  @override
  Widget build(final BuildContext context) {
    print('restaurantId : ${widget.restaurantId}');

    return SafeArea(
      child: Container(
          height: 700.w,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20.w),
          ),
          child: _body(context)),
    );
  }

  Widget _body(final BuildContext context) {
    final isResting = ref.watch(
      restaurantDetailControllerProvider
          .select((final state) => state.restaurantData?.isResting),
    );
    id = widget.food?.foodId ?? 0;
    final foodCount = _getFoodCount(widget.food);

    Food food = Food(
      id: widget.food?.foodId ?? 0,
      image: widget.food?.image,
      price: widget.food?.price ?? 0,
      originPrice: widget.food?.oldPrice ?? 0,
      oldPrice: widget.food?.oldPrice ?? 0,
      maxOrderCount: widget.food?.maxOrderCount ?? 0,
      state: widget.food?.state ?? 0,
      lunchBoxFee: widget.food?.lunchBoxFee ?? 0,
      lunchBoxId: widget.food?.lunchBoxId ?? 0,
      specialActive: 1,
      percent: widget.food?.percent ?? 0,
      seckillId: widget.food?.seckillId ?? 0,
      seckillActive: widget.food?.seckillActive ?? 0,
      originProfit: widget.food?.originalProfit ?? 0,
      profit: widget.food?.profit ?? 0,
      foodType: widget.food?.foodType ?? 0,
    );

    return Stack(
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: 15.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(20.w),
                  topLeft: Radius.circular(20.w),
                ),
              ),
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      padding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.w),
                      child: Icon(
                        Icons.clear,
                        color: AppColors.textSecondColor,
                        size: 26.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: NotificationListener<ScrollNotification>(
                onNotification: (final ScrollNotification notification) {
                  if (notification is ScrollUpdateNotification) {
                    // 判断是否滚动到底部
                    if (notification.metrics.pixels ==
                        notification.metrics.maxScrollExtent) {
                      if (ref.watch(canCommentDataProvider)) {
                        _onScrollToBottom();
                      }
                    }
                  }
                  return true;
                },
                child: SingleChildScrollView(
                  child: Container(
                    padding: EdgeInsets.only(right: 10.w, left: 10.w, bottom: 15.w),
                    color: Colors.white,
                    child: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            showImageViewer(
                              context,
                              imageUrls: [widget.food?.image ?? ''],
                              heroTagPrefix: 'chat_image',
                            );
                          },
                          child: Container(
                            height: 360.w,
                            color: Colors.white,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(10.w),
                              child: PrefectImageWidth(
                                imageUrl: widget.food?.image ?? '',
                                fit: BoxFit.cover,
                                width: MediaQuery.of(context).size.width,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 12.w,
                        ),

                        Container(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              SizedBox(
                                width: MediaQuery.of(context).size.width * 0.65,
                                child: Text(
                                  widget.food?.foodName ?? '',
                                  style: TextStyle(
                                      color: Colors.black, fontSize: soBigSize),
                                ),
                              ),
                              Row(
                                children: [

                                  ref.watch(languageProvider) == 'zh' ? Text('￥',style: TextStyle(decoration: TextDecoration.lineThrough,fontSize: littleSize,color: AppColors.textSecondaryColor)):SizedBox(),
                                  Text('${widget.food?.oldPrice}',  style: TextStyle(fontSize: mainSize,decoration: TextDecoration.lineThrough,color: AppColors.textSecondaryColor),),
                                  ref.watch(languageProvider) == 'ug' ? Text('￥',style: TextStyle(decoration: TextDecoration.lineThrough,fontSize: littleSize,color: AppColors.textSecondaryColor)):SizedBox(),
                                  SizedBox(width: 10.w,),

                                  ref.watch(languageProvider) == 'zh' ? Row(
                                    children: [
                                      SizedBox(
                                        width: 3.w,
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(top: 4.w),
                                        child: Text(
                                          '¥',
                                          style: TextStyle(
                                            fontSize: mainSize,
                                            fontFamily: AppConstants.numberFont,
                                            color: AppColors.redColor,
                                          ),
                                        ),
                                      )
                                    ],
                                  ):SizedBox(),

                                  Text(
                                    '${widget.food?.price}',
                                    style: TextStyle(
                                      fontSize: 24.sp,
                                      fontWeight: FontWeight.bold,
                                      fontFamily: AppConstants.numberFont,
                                      color: AppColors.redColor,
                                    ),
                                  ),

                                  ref.watch(languageProvider) == 'ug' ? Row(
                                    children: [
                                      SizedBox(
                                        width: 3.w,
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(top: 4.w),
                                        child: Text(
                                          '¥',
                                          style: TextStyle(
                                            fontSize: mainSize,
                                            fontFamily: AppConstants.numberFont,
                                            color: AppColors.redColor,
                                          ),
                                        ),
                                      )
                                    ],
                                  ):SizedBox()
                                ],
                              )
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 20.w,
                        ),
                        _countPart(food, foodCount),
                        SizedBox(
                          height: 20.w,
                        ),

                        Container(
                          alignment: ref.watch(languageProvider) == 'ug' ? Alignment.centerRight : Alignment.centerLeft,
                          child: RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: S.current.one_time_can_buy_pre,
                                  style: TextStyle(
                                    fontSize: mainSize, // 34rpx / 2
                                    color: AppColors.textSecondColor, // 保留特殊蓝色
                                    fontFamily: AppConstants.mainFont, // 添加字体family
                                  ),
                                ),
                                TextSpan(
                                  text: '${widget.food?.maxOrderCount ?? 0}',
                                  style: TextStyle(
                                      fontSize: mainSize, // 30rpx / 2
                                      color: AppColors.redColor, // 使用全局文本颜色
                                      fontFamily:
                                      AppConstants.mainFont, // 添加字体family
                                      fontWeight: FontWeight.bold),
                                ),
                                TextSpan(
                                  text: S.current.one_time_can_buy_after,
                                  style: TextStyle(
                                    fontSize: mainSize, // 30rpx / 2
                                    color: AppColors.textSecondColor, // 使用全局文本颜色
                                    fontFamily: AppConstants.mainFont, // 添加字体family
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        SizedBox(
                          height: 38.w,
                        ),

                        // 套餐美食列表
                        // if (widget.food?.foodType == 2 && widget.food?.comboFoodItems != null && widget.food!.comboFoodItems!.isNotEmpty)
                        //   ComboFoodListWidget(
                        //     comboFoodItems: widget.food!.comboFoodItems!,
                        //     maxDisplayCount: 10,
                        //     showBackground: true,
                        //   ),
                        // Container(
                        //   alignment: ref.watch(languageProvider) == 'ug'
                        //       ? Alignment.centerRight
                        //       : Alignment.centerLeft,
                        //   child: Text(
                        //     widget.food?.description ?? '',
                        //     style: TextStyle(
                        //       color: AppColors.textSecondColor,
                        //       fontSize: mainSize,
                        //     ),
                        //   ),
                        // ),
                        // SizedBox(
                        //   height: 12.w,
                        // ),
                        Container(
                          alignment: ref.watch(languageProvider) == 'ug'
                              ? Alignment.centerRight
                              : Alignment.centerLeft,
                          child: Text(
                            S.current.comments,
                            style:
                            TextStyle(color: Colors.black, fontSize: soBigSize),
                          ),
                        ),
                        SizedBox(
                          height: 12.w,
                        ),
                        Container(
                          alignment: ref.watch(languageProvider) == 'ug'
                              ? Alignment.centerRight
                              : Alignment.centerLeft,
                          child: Wrap(
                            spacing: 10.w,
                            runSpacing: 10.w,
                            children: List.generate(
                              (ref
                                  .watch(restaurantCommentListProvider)
                                  .value
                                  ?.type ??
                                  [])
                                  .length,
                                  (final index) => _tabWidget(
                                  index,
                                  ref
                                      .watch(restaurantCommentListProvider)
                                      .value!
                                      .type![index]),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 12.w,
                        ),
                        if ((ref.watch(commentItemsProvider)).isNotEmpty)
                          ...List.generate(
                            (ref.watch(commentItemsProvider)).length,
                                (final index) => _item(
                              context,
                              ref.watch(commentItemsProvider)[index],
                            ),
                          ),
                        if ((ref.watch(commentItemsProvider)).isEmpty)
                          Container(
                            padding: EdgeInsets.only(top: 15.w),
                            height: 100.w,
                            child: Text(
                              S.current.comment_no_data,
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: titleSize,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        Positioned(
          bottom: 8.w,
          right: 10.w,
          left: 10.w,
          child: RestaurantBottomBarWidget(
            onCloseModal: () {
              Navigator.of(context).pop();
            },
            restaurantId: widget.restaurantId,
          ),
        )
      ],
    );
  }

  Widget _countPart(Food food, int foodCount) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            S.current.buy_count,
            style: TextStyle(color: Colors.black, fontSize: soBigSize),
          ),
          QuantityControlWidget(
            foodCount: foodCount,
            foodId: widget.food?.foodId,
            food: food,
            relationsExists: false,
            isResting: false,
            onAdd: _handleAddFood,
            onRemove: _handleRemoveFood,
            onAddMinCount: _handleAddMinCountFood,
            onSpecSelect: (final food) => _handleSpecSelect(food, false),
          ),
        ],
      ),
    );
  }

  /// 获取购物车中主商品的数量
  int _getFoodCount(final SpecialItems? food) {
    return _getItemCount(food);
  }

  /// 获取购物车中指定商品的数量
  int _getItemCount(final SpecialItems? food) {
    if (food == null) return 0;
    final cartItems = ref.watch(shoppingCartProvider);
    try {
      final item = cartItems.firstWhere(
        (final item) => item.id == food.foodId,
        orElse: () => SelectFoodItem(count: 0),
      );
      return item.count ?? 0;
    } catch (e) {
      debugPrint('Error in _getItemCount: $e');
      return 0;
    }
  }

  /// 处理最小购买数量添加
  void _handleAddMinCountFood(final Food food) {
    // 一次性添加最小数量的食品
    for (int i = 0; i < (food.minCount ?? 1); i++) {
      ref.read(shoppingCartProvider.notifier).addFoodToCart(
            foodItem: food,
          );
    }

    // 如果是主食品，标记为直接添加
    DirectlyAddedFoods.add(food.id);
  }

  /// 处理规格选择
  void _handleSpecSelect(final Food food, final bool isResting) async {
    // 显示规格选择弹窗
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (final BuildContext context) {
        return SpecModalWidget(
          foodItem: food,
          restaurantId: widget.restaurantId,
          isResting: isResting,
          onClose: () {
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  /// 处理减少食品逻辑
  void _handleRemoveFood(final int? foodId) {
    ref.read(shoppingCartProvider.notifier).removeFoodCount(
          foodId: foodId ?? 0,
        );
  }

  /// 处理添加食品逻辑
  void _handleAddFood(
    final int foodCount,
    final int? foodId,
    final Food? food,
    final bool relationsExists, [
    final BuildContext? btnContext,
  ]) {
    if (foodCount == 0) {
      if (food != null) {
        // 只有当添加的是主食品（不是关联食品）时，才记录为直接添加
        // if (food.id == widget.foodItem?.id) {
        DirectlyAddedFoods.add(foodId);
        // }

        // 添加到购物车
        ref.read(shoppingCartProvider.notifier).addFoodToCart(
              foodItem: food,
            );

        // 执行加入购物车动画
        // _playAddToCartAnimation(btnContext);

        // 如果有关联商品则显示，但只在添加主食品时
        // if (relationsExists && food.id == widget.foodItem?.id) {
        //   _showRelationsWithAnimation();
        // }
      }
    } else {
      // 增加数量
      ref.read(shoppingCartProvider.notifier).addFoodCount(
            foodId: foodId ?? 0,
          );

      // 执行加入购物车动画
      // _playAddToCartAnimation(btnContext);
    }
  }

  // 方法在滚动到底部时触发
  Future<void> _onScrollToBottom() async {
    page = page + 1;
    ref.read(restaurantCommentListProvider.notifier).fetchRestaurantCommentList(
          page: page,
          id: widget.restaurantId,
          type: type,
          foodId: id,
        );
    print("滚动到底部了！----");
  }

  int currentTab = 0;
  Widget _tabWidget(final int index, final Type type) {
    return InkWell(
      onTap: () {
        print('index $index, widget.restaurantId ${widget.restaurantId}');

        currentTab = index;
        page = 1;
        this.type = type.type ?? 0;
        ref
            .read(restaurantCommentListProvider.notifier)
            .fetchRestaurantCommentList(
              page: page,
              id: widget.restaurantId,
              type: this.type,
              foodId: id,
            );
        // ref.watch(canCommentDataProvider.notifier).state = true;
        setState(() {});
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30.w),
          color: index == currentTab
              ? AppColors.baseGreenColor
              : AppColors.baseBackgroundColor,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${type.name}',
              style: TextStyle(
                fontSize: ref.read(languageProvider) == 'ug' ?  mainSize : titleSize,
                color: index == currentTab
                    ? AppColors.baseBackgroundColor
                    : Colors.black,
                fontWeight: index == currentTab ? FontWeight.bold : null,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
            ),
            SizedBox(
              width: 4.w,
            ),
            Text(
              '(${type.count})',
              style: TextStyle(
                fontSize: secondSize,
                color: index == currentTab
                    ? AppColors.baseBackgroundColor
                    : Colors.black,
                fontWeight: index == currentTab ? FontWeight.bold : null,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
            ),
          ],
        ),
      ),
    );
  }

  Widget _item(final BuildContext context, final Items item) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 12.w),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(color: Colors.grey.shade100, width: 1.0),
            ),
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(10.w),
                child: PrefectImage(
                  imageUrl: item.userAvatar ?? '',
                  fit: BoxFit.cover,
                  width: 52.w,
                  height: 52.w,
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${(item.userName == null || item.userName == '') ? '******' : item.userName}',
                          style: TextStyle(
                            fontSize: mainSize,
                            color: Colors.black,
                          ),
                        ),
                        Text(
                          item.createdAt ?? '',
                          style: TextStyle(
                            fontSize: secondSize,
                            color: AppColors.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 5.w,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          S.current.evaluate_in_general,
                          style: TextStyle(
                            fontSize: secondSize,
                            color: Colors.black,
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            StarRating(
                              rating: double.parse('${item.star ?? 5}'),
                              size: 18.sp, // 32rpx / 2
                              gap: 4.0, // 设置星星间隔
                              hideTip: true,
                            ),
                            SizedBox(
                              width: 15.w,
                            ),
                            Container(
                              width: 111.w,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6.w),
                                color: AppColors.baseBackgroundColor,
                              ),
                              padding: EdgeInsets.symmetric(
                                horizontal: 6.w,
                                vertical: 3.w,
                              ),
                              child: Text(
                                '${item.foodName}',
                                style: TextStyle(
                                  fontSize: mainSize,
                                  color: AppColors.baseGreenColor,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.start,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    if ((item.star ?? 5) < 5)
                      Column(
                        children: [
                          SizedBox(
                            height: 5.w,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                S.current.evaluate_taste,
                                style: TextStyle(
                                  fontSize: secondSize,
                                  color: Colors.black,
                                ),
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  StarRating(
                                    rating:
                                        double.parse('${item.foodStar ?? 5}'),
                                    size: 18.sp, // 32rpx / 2
                                    gap: 4.0, // 设置星星间隔
                                    hideTip: true,
                                  ),
                                  SizedBox(
                                    width: 15.w,
                                  ),
                                  Container(
                                    width: 111.w,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    if ((item.star ?? 5) < 5)
                      Column(
                        children: [
                          SizedBox(
                            height: 5.w,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                S.current.evaluate_packing,
                                style: TextStyle(
                                  fontSize: secondSize,
                                  color: Colors.black,
                                ),
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  StarRating(
                                    rating:
                                        double.parse('${item.boxStar ?? 5}'),
                                    size: 18.sp, // 32rpx / 2
                                    gap: 4.0, // 设置星星间隔
                                    hideTip: true,
                                  ),
                                  SizedBox(
                                    width: 15.w,
                                  ),
                                  Container(
                                    width: 111.w,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
        if (item.text != null && item.text != '')
          Container(
            alignment: ref.watch(languageProvider) == 'ug'
                ? Alignment.centerRight
                : Alignment.centerLeft,
            child: Text(
              '${item.text}',
              style: TextStyle(fontSize: mainSize, color: Colors.black),
            ),
          ),
        if ((item.images ?? []).isNotEmpty)
          Column(
            children: List.generate(
              (item.images ?? []).length,
              (final imageIndex) => Column(
                children: [
                  SizedBox(
                    height: 12.w,
                  ),
                  InkWell(
                    onTap: () {
                      showImageViewer(
                        context,
                        imageUrls: [item.images![imageIndex]],
                        heroTagPrefix: 'chat_image',
                      );
                    },
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10.w),
                      child: PrefectImage(
                        imageUrl: item.images![imageIndex],
                        fit: BoxFit.cover,
                        width: MediaQuery.of(context).size.width,
                        height: 190.w,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        Column(
          children: List.generate(
            (item.replies ?? []).length,
            (final repIndex) => Column(
              children: [
                SizedBox(
                  height: 12.w,
                ),
                Container(
                  alignment: ref.watch(languageProvider) == 'ug'
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  padding: EdgeInsets.symmetric(
                    horizontal: 10.w,
                    vertical: 10.w,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6.w),
                    color: AppColors.baseBackgroundColor,
                  ),
                  child: RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: S.current.reply_res,
                          style: TextStyle(
                            color: Colors.indigo,
                            fontSize: mainSize,
                            fontFamily: AppConstants.mainFont,
                            height: 1.2,
                          ),
                        ),
                        TextSpan(
                          text: '${item.replies?[repIndex].text}',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: mainSize,
                            fontFamily: AppConstants.mainFont,
                            height: 1.2,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 分享应用
  Future<void> shareApp() async {
    LoadingDialog().show();
    final result = await WechatUtil().shareMiniProgram(
      thumbData: await ImageUtil.getImageFromUrl(
        'https://cdns.mulazim.com/wechat_mini/share.jpg',
      ),
      path: "pages/index/index",
      title: S.current.app_description,
      description: S.current.app_description,
    );
    if (result) {
      BotToast.showText(text: S.current.about_share_success);
    } else {
      BotToast.showText(text: S.current.about_share_failed);
    }
    LoadingDialog().hide();
  }
}
