import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:async';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/features/home/<USER>/parts/dialog_part.dart';
import 'package:user_app/features/my_order/pages/my_order_detail/cancel_order_detail.dart';
import 'package:user_app/features/my_order/pages/my_order_detail/my_order_detail_controller.dart';
import 'package:user_app/features/my_order/pages/my_order_detail/widgets/index.dart';
import 'package:user_app/features/my_order/pages/my_order_detail/widgets/marketing_part_widget.dart';
import 'package:user_app/features/my_order/pages/my_order_detail/widgets/total_part_widget.dart';
import 'package:user_app/features/my_order/providers/order_detail_provider.dart';
import 'package:user_app/generated/l10n.dart';

class MyOrderDetailPage extends ConsumerStatefulWidget {
  const MyOrderDetailPage({super.key, required this.orderId});
  final int orderId;

  @override
  ConsumerState createState() => _MyOrderDetailPageState();
}

class _MyOrderDetailPageState extends ConsumerState<MyOrderDetailPage> {
  static final GlobalKey _mapKey = GlobalKey();
  double? screenWidth;
  Timer? _autoRefreshTimer;

  @override
  void initState() {
    super.initState();

    // 启动15秒自动刷新定时器
    _startAutoRefreshTimer();

    SchedulerBinding.instance.addPostFrameCallback((final _) async {
      await Future.delayed(Duration(seconds: 1));
      OrderDetailData? data = ref.watch(orderDetailProvider).value;
      if (data != null &&
          (data.delayed != null) &&
          (data.delayed!.agreeState == 1)) {
        showRequireDialogTask(
          data.id,
          data.adminPhone,
          data.delayed,
          screenWidth!,
          context,
        );
      }
    });
  }

  @override
  void dispose() {
    // 取消自动刷新定时器
    _autoRefreshTimer?.cancel();
    super.dispose();
  }

  /// 启动自动刷新定时器
  void _startAutoRefreshTimer() {
    _autoRefreshTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      // 检查页面是否仍然活跃
      if (mounted) {
        // 刷新订单详情数据
        ref
            .read(orderDetailProvider.notifier)
            .orderDetailInfo(orderId: widget.orderId);
      } else {
        // 如果页面已销毁，取消定时器
        timer.cancel();
      }
    });
  }

  @override
  Widget build(final BuildContext context) {
    final controller =
        ref.read(myOrderDetailControllerProvider(widget.orderId).notifier);
    final state = ref.watch(myOrderDetailControllerProvider(widget.orderId));
    final orderDetailAsync = ref.watch(orderDetailProvider);
    screenWidth = MediaQuery.of(context).size.width;

    // 判断手势操作是否在规划图之内
    bool isPointerOnMap(final Offset localPosition) {
      if (_mapKey.currentContext?.findRenderObject() is RenderBox) {
        // 获取规划图区域在屏幕上的位置和大小
        RenderBox mapBox =
            _mapKey.currentContext?.findRenderObject() as RenderBox;
        Offset mapPosition = mapBox.localToGlobal(Offset.zero);
        double mapLeft = mapPosition.dx;
        double mapTop = mapPosition.dy;
        double mapWidth = mapBox.size.width;
        double mapHeight = mapBox.size.height;

        if (localPosition.dx >= mapLeft &&
            localPosition.dx <= mapLeft + mapWidth &&
            localPosition.dy >= (mapTop / 1.5) &&
            localPosition.dy <= (mapTop / 1.5) + mapHeight) {
          // 触摸点在规划图区域内
          return true;
        } else {
          // 触摸点不在规划图区域内
          return false;
        }
      } else {
        return false;
      }
    }

    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: AppColors.baseBackgroundColor,
        appBar: AppBar(
          backgroundColor: AppColors.baseGreenColor,
          foregroundColor: Colors.white,
          title: Text(
            S.current.order_detail,
            style: TextStyle(color: Colors.white, fontSize: titleSize),
          ),
        ),
        body: orderDetailAsync.when(
          data: (final data) {
            bool showTimePart =
                ((data?.state ?? 0) == 1 && (data?.expired ?? 0) == 0)
                    ? true
                    : false;

            // 如果显示倒计时部分，启动倒计时
            if (showTimePart &&
                data != null &&
                data.expiredTime != null &&
                data.expiredTime! > 0) {
              // 延迟启动倒计时，确保页面已完全加载
              Future.microtask(() {
                if (mounted) {
                  controller.startCountdown(data.expiredTime!, widget.orderId);
                }
              });
            }

            // 计算订单状态显示文本
            String logStr = '';
            if (data?.deliveryType == 1) {
              if ((data?.state ?? 0) < 3 && data?.expired == 1) {
                logStr = S.current.order_time_out;
              } else {
                if ((data?.orderStateLog ?? []).isNotEmpty) {
                  logStr = data?.orderStateLog?[0].name ?? '';
                }
              }
            } else if (data?.deliveryType == 2) {
              if ((data?.state ?? 0) < 3 && data?.expired == 1) {
                logStr = S.current.order_time_out;
              } else {
                if ((data?.state ?? 0) == 7) {
                  logStr = S.current.order_done;
                } else {
                  if ((data?.orderStateLog ?? []).isNotEmpty) {
                    logStr = data?.orderStateLog?[0].name ?? '';
                  }
                }
              }
            }
            return Listener(
              onPointerDown: (final event) {
                if ((data != null && (data.shipper?.state ?? 0) == 6) &&
                    isPointerOnMap(event.localPosition)) {
                  // 触摸点在规划图区域内，禁用滚动
                  controller.setCanScroll(false);
                } else {
                  controller.setCanScroll(true);
                }
              },
              onPointerUp: (final event) {
                controller.setCanScroll(true);
              },
              child: SingleChildScrollView(
                physics: state.canScroll
                    ? const ClampingScrollPhysics()
                    : NeverScrollableScrollPhysics(),
                child: Column(
                  children: [
                    if (showTimePart && data != null)
                      TimePartWidget(
                        data: data,
                        restSecond: state.restSecond,
                        onTap: () async {
                          await showOrderStateBottomPanel(
                            context,
                            data.orderStateLog,
                            data.orderStateLog?[0].name ?? '',
                          );
                        },
                      ),
                    if (data != null &&
                        (data.orderStateLogNew?.items ?? []).isNotEmpty)
                      OrderStateNewWidget(data: data),
                    if (data != null &&
                        (data.delayed != null) &&
                        (data.delayed!.agreeState == 1))
                      InkWell(
                        onTap: () {
                          showRequireDialogTask(
                            data.id,
                            data.adminPhone,
                            data.delayed,
                            screenWidth!,
                            context,
                          );
                        },
                        child: Container(
                          margin: EdgeInsets.only(
                              right: 10.w, left: 10.w, top: 10.w),
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 10.w,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.w),
                            gradient: LinearGradient(
                              colors: [
                                AppColors.redColor,
                                Colors.orange,
                              ], // 渐变的颜色列表
                              begin: Alignment.centerRight, // 渐变开始位置
                              end: Alignment.centerLeft, // 渐变结束位置
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    color: Colors.white,
                                  ),
                                  SizedBox(
                                    width: 5.w,
                                  ),
                                  Text(
                                    S.current.require_delay_title,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: mainSize,
                                    ),
                                  ),
                                ],
                              ),
                              // Icon(Icons.arrow_forward,color: Colors.white,),
                              Image.asset(
                                'assets/images/jiantou.png',
                                fit: BoxFit.cover,
                                width: 32.w,
                                height: 32.w,
                              ),
                            ],
                          ),
                        ),
                      ),
                    if (!showTimePart &&
                        data != null &&
                        data.orderStateLog != null &&
                        data.orderStateLog!.isNotEmpty)
                      OrderStateWidget(
                        orderDetailData: data,
                        onTap: () async {
                          await showOrderStateBottomPanel(
                            context,
                            data.orderStateLog,
                            logStr,
                          );
                        },
                      ),
                    if (data != null) FeaturePartWidget(orderDetailData: data),
                    DiscountQRCodeWidget(),
                    if (data != null && (data.shipper?.state ?? 0) == 6)
                      MapPartWidget(key: _mapKey, data: data),
                    if (data != null) FoodsPartWidget(orderDetailData: data),
                    if (data != null && (data.marketing ?? []).isNotEmpty)
                      MarketingPartWidget(orderDetailData: data),
                    if (data != null) TotalPartWidget(orderDetailData: data),
                    if (data != null &&
                        (data.state != 8) &&
                        (data.state != 9) &&
                        (data.partRefundType == 2))
                      _refundPart(
                        partRefundId: data.partRefundId ?? 0,
                        partRefundAmount: data.partRefundAmount ?? 0,
                      ),
                    if (data != null) DeliveryPartWidget(orderDetailData: data),
                    if (data != null) OrderPartWidget(orderDetailData: data),
                  ],
                ),
              ),
            );
          },
          error: (final error, final stackTrace) {
            print('stackTrace $stackTrace');
            return Center(
              child: Column(
                children: [
                  Text('Error: $error'),
                  Text('StackTrace: $stackTrace'),
                ],
              ),
            );
          },
          loading: () {
            return const Center(
              child: CircularProgressIndicator(),
            );
          },
        ),
      ),
    );
  }

  Widget _refundPart({
    required final int partRefundId,
    required final num partRefundAmount,
  }) {
    return InkWell(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (final context) => CancelOrderDetail(
              partRefundId: partRefundId,
            ),
          ),
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 10.w),
        margin: EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.w),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              S.current.refund_fee_title,
              style: TextStyle(color: Colors.black, fontSize: mainSize),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '10',
                  style: TextStyle(color: Colors.red, fontSize: titleSize),
                ),
                Text(
                  '￥',
                  style: TextStyle(color: Colors.red, fontSize: titleSize),
                ),
                SizedBox(
                  width: 10.w,
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.textSecondColor,
                  size: 16.sp,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
