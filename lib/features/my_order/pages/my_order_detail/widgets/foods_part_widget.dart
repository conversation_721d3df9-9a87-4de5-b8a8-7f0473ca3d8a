import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/data/models/restaurant/foods_list_model.dart';
import 'package:user_app/features/order/pages/submit_order_page.dart';
import 'package:user_app/features/order/widgets/combo_food_items_widget.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/common_widgets.dart';
import 'package:user_app/features/order/widgets/spec_selected_options_widget.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';

class FoodsPartWidget extends ConsumerWidget {
  final OrderDetailData orderDetailData;

  const FoodsPartWidget({
    Key? key,
    required this.orderDetailData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: EdgeInsets.only(top: 15.w),
      margin: EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.w),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              context.push(
                AppPaths.restaurantDetailPage,
                extra: {
                  'restaurantId': orderDetailData.restaurantId ?? 0,
                  'buildingId': orderDetailData.buildingId ?? 0,
                  'ids': [],
                },
              );
            },
            child: Container(
              padding: EdgeInsets.only(bottom: 10.w, right: 10.w, left: 10.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Text('${orderDetailData.restaurantName}'),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.sp,
                    color: AppColors.textSecondColor,
                  ),
                ],
              ),
            ),
          ),
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          Column(
            children: List.generate(
              (orderDetailData.orderDetail?.foods ?? []).length,
              (index) => _foodItem(
                orderDetailData.orderDetail!.foods![index],
                index,
              ),
            ),
          ),
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          _labelItem(
            S.current.shipment_method,
            orderDetailData.deliveryType == 1
                ? S.current.deliver_mulazim
                : S.current.self_take,
            false,
            null,
            null,
          ),
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          _labelItem(
            S.current.shipment_fee,
            '${orderDetailData.shipment}',
            true,
            (orderDetailData.shipmentSteps ?? []).isNotEmpty
                ? () {
                    List<Tag> tags = [];
                    for (int i = 0;
                        i < (orderDetailData.shipmentSteps ?? []).length;
                        i++) {
                      Tag thisTag = Tag(
                        minDeliveryPrice:
                            orderDetailData.shipmentSteps![i].minDeliveryPrice,
                        distanceEnd:
                            orderDetailData.shipmentSteps![i].distanceEnd,
                        price: orderDetailData.shipmentSteps![i].price,
                      );
                      tags.add(thisTag);
                    }
                    showShipmentReductionDialog(tags, context, ref);
                  }
                : null,
            '${orderDetailData.originalShipment}',
          ),
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          if (orderDetailData.lunchBoxFee != null &&
              (orderDetailData.lunchBoxFee ?? 0) > 0)
            _labelItem(
              S.current.lunch_box_fee,
              '${orderDetailData.lunchBoxFee}',
              true,
              null,
              null,
            ),
          // Divider(height: 0.5.w, color: AppColors.searchBackColor),
          // _labelItem(
          //     S.current.shipper_total, '${orderDetailData.actualPaid}', true,null,null),
        ],
      ),
    );
  }

  Widget _foodItem(OrderDetailFoods orderDetailFoods, int index) {
    // 判断是否是套餐商品
    final isComboFood = orderDetailFoods.foodType == 2 &&
        orderDetailFoods.comboFoodItems != null &&
        orderDetailFoods.comboFoodItems!.isNotEmpty;
    return Container(
      padding: EdgeInsets.all(10.w),
      child: Column(
        children: [
          Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(10.w),
                child: PrefectImage(
                  imageUrl: orderDetailFoods.img ?? '',
                  fit: BoxFit.cover,
                  width: 90.w,
                  height: 70.w,
                ),
              ),
              SizedBox(
                width: 10.w,
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('${orderDetailFoods.name}'),
                    SizedBox(
                      height: 2.w,
                    ),
                    Text(
                      'x${orderDetailFoods.number}',
                      style:
                          TextStyle(fontSize: soBigSize, color: Colors.black),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Row(
                    children: [
                      SizedBox(
                        width: 10.w,
                      ),
                      if (orderDetailFoods.originalPrice != null &&
                          (orderDetailFoods.originalPrice ?? 0) >
                              (orderDetailFoods.price ?? 0))
                        Row(
                          children: [
                            Text(
                              '￥${orderDetailFoods.originalPrice}',
                              style: TextStyle(
                                fontSize: 15.sp,
                                color: AppColors.dialogCancelColor,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                            SizedBox(
                              width: 10.w,
                            ),
                          ],
                        ),
                      Text(
                        '￥${orderDetailFoods.price}',
                        style:
                            TextStyle(fontSize: titleSize, color: Colors.black),
                      ),
                    ],
                  ),
                  //套餐 展开/折叠按钮
                  if (isComboFood)
                    Consumer(
                      builder: (
                        final contex,
                        final WidgetRef ref,
                        final child,
                      ) {
                        final isExpanded = ref.watch(
                          comboExpandedStateProvider(index),
                        );

                        return InkWell(
                          onTap: () {
                            ref
                                .read(
                                  comboExpandedStateProvider(index).notifier,
                                )
                                .state = !isExpanded;
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: AppColors.backgroundColor,
                              borderRadius: BorderRadius.circular(20.r),
                            ),
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            child: Row(
                              children: [
                                Text(
                                  S.current.snow_detail, // "详情"
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(width: 4.w),
                                AnimatedRotation(
                                  duration: Duration(milliseconds: 250),
                                  curve: Curves.easeInOut,
                                  turns: isExpanded ? 0.5 : 0, // 0.5 = 180度
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: AppColors.primary,
                                      borderRadius: BorderRadius.circular(
                                        20.r,
                                      ),
                                    ),
                                    child: Icon(
                                      Icons.keyboard_arrow_down,
                                      size: 20.sp,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                ],
              ),
            ],
          ),

          // 规格选择信息显示 - 与微信小程序逻辑一致
          if (orderDetailFoods.foodType == 1 &&
              orderDetailFoods.selectedFood?.specOptions != null &&
              orderDetailFoods.selectedFood!.specOptions!.isNotEmpty)
            Container(
              width: double.infinity,
              margin: EdgeInsets.only(top: 8.h),
              child: SpecSelectedOptionsWidget.withBackground(
                specSelectedOptions: orderDetailFoods.selectedFood!.specOptions!
                    .map(
                      (option) => {
                        'spec_type_id': option.specType?.id ?? 0,
                        'spec_option_id': option.id ?? 0,
                        'name': option.name ?? '',
                        'price': option.price ?? 0,
                      },
                    )
                    .toList(),
              ),
            ),

          if (isComboFood)
            // 套餐商品布局
            _buildComboFoodItem(
              orderDetailFoods,
              index,
            ),
        ],
      ),
    );
  }

  /// 构建套餐美食项目
  /// 包含主要信息和可展开的详情列表
  Widget _buildComboFoodItem(
    final OrderDetailFoods item,
    final int index,
  ) {
    return Consumer(
      builder: (final context, final ref, final child) {
        // 获取展开状态 - 临时使用本地状态，后续可以移到provider中
        final isExpanded = ref.watch(comboExpandedStateProvider(index));

        return AnimatedContainer(
          duration: Duration(milliseconds: 250),
          height: isExpanded ? null : 0,
          child: isExpanded
              ? ComboFoodItemsWidget(comboItems: item.comboFoodItems)
              : null,
        );
      },
    );
  }

  Widget _labelItem(
    String label,
    String val,
    bool isMoney,
    Function? onClick,
    String? originalPrice,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(label),
              if (onClick != null)
                InkWell(
                  onTap: () {
                    onClick.call();
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.w),
                    child: Icon(
                      Icons.info_outline,
                      color: Colors.grey,
                      size: 24.sp,
                    ),
                  ),
                ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if (originalPrice != null &&
                  num.parse(originalPrice) > num.parse(val))
                Container(
                  padding: EdgeInsets.only(top: 3.w),
                  child: Text(
                    '￥$originalPrice',
                    style: TextStyle(
                      fontSize: 15.sp,
                      color: AppColors.dialogCancelColor,
                      decoration: TextDecoration.lineThrough,
                    ),
                  ),
                ),
              SizedBox(
                width: 8.w,
              ),
              Text(
                '${isMoney ? '￥' : ''}$val',
                style: TextStyle(fontSize: titleSize, color: Colors.black),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
