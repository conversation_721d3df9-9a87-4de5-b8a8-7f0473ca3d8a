import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/generated/l10n.dart';


class TotalPartWidget extends ConsumerStatefulWidget {
  TotalPartWidget({super.key,required this.orderDetailData,});
  final OrderDetailData orderDetailData;

  @override
  ConsumerState createState() => _TotalPartWidgetState();
}

class _TotalPartWidgetState extends ConsumerState<TotalPartWidget> {

  String preferentialPrice = '0';

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    List<Marketing> marketing = widget.orderDetailData.marketing ?? [];

    List<OtherMarketing> otherMarketing = widget.orderDetailData.otherMarketing ?? [];
    num preferential = 0;

    for(int i = 0; i < marketing.length; i++){
      preferential = preferential + (marketing[i].price ?? 0);
    }

    for(int i = 0; i < otherMarketing.length; i++){
      preferential = preferential + (otherMarketing[i].price ?? 0);
    }

    Coupon? coupon = widget.orderDetailData.coupon;
    if(coupon != null){
      preferential = preferential + num.parse((coupon.price ?? '0').toString());
    }

    preferentialPrice = FormatUtil.formatPrice(preferential);
  }

  @override
  Widget build(BuildContext context) {

    return Container(
      padding: EdgeInsets.symmetric(vertical: 10.w),
      margin: EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(10.w)),
      child: Column(
        children: [
          _labelItem(
              S.current.discount_total_fee, preferentialPrice,null,Colors.red,true),
          Divider(height: 0.5.w, color: AppColors.searchBackColor),
          _labelItem(
              S.current.order_total_fee, '${widget.orderDetailData.actualPaid}', FormatUtil.formatPrice((widget.orderDetailData.actualPaid ?? 0) + num.parse(preferentialPrice)),AppColors.baseGreenColor,false),
        ],
      ),
    );
  }

  Widget _labelItem(String label, String val, String? originalPrice,Color color,isAll) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label,style: TextStyle(color: isAll ? color : Colors.black),),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              if(originalPrice != null && (num.parse(originalPrice) > (num.parse(val))))
                Container(
                    padding: EdgeInsets.only(top: 3.w),
                    child: Text('￥$originalPrice',style: TextStyle(fontSize: 15.sp,color: AppColors.dialogCancelColor,decoration: TextDecoration.lineThrough,),)
                ),
              SizedBox(width: 8.w,),
              Directionality(
                textDirection: TextDirection.rtl,
                child: Row(
                  children: [
                    Text('￥$val',style: TextStyle(fontSize: titleSize,fontWeight:!isAll ? FontWeight.bold :null,color: color),),
                    if(isAll)Text('-',style: TextStyle(fontSize: titleSize,color: color),),

                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}