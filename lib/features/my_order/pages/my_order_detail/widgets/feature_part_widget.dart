import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/icon_font.dart';
import 'package:user_app/data/models/my_order/order_detail_model.dart';
import 'package:user_app/features/address/dialog/animate_confirm_dialog.dart';
import 'package:user_app/features/my_order/pages/my_order_detail/my_order_detail_controller.dart';
import 'package:user_app/features/restaurant/models/come_again_item_model.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';

class FeaturePartWidget extends ConsumerWidget {
  final OrderDetailData orderDetailData;

  const FeaturePartWidget({
    Key? key,
    required this.orderDetailData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用新的模型处理"再来一单"数据
    final comeAgainItems = ComeAgainItemModel.fromOrderDetailFoods(
        orderDetailData.orderDetail?.foods ?? []);
    return Container(
      alignment: ref.watch(languageProvider) == 'ug'
          ? Alignment.centerRight
          : Alignment.centerLeft,
      margin: EdgeInsets.only(right: 10.w, left: 10.w, top: 10.w),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(10.w)),
      child: Wrap(
        children: [
          _featureItem(
              IconFont.mlzdianhua, '', S.current.connect_to, Colors.black,
              () async {
            await showMobileListBottomPanel(context, orderDetailData);
          }),
          if ((orderDetailData.state ?? 0) > 2)
            _featureItem(Icons.forum, 'assets/images/comment.png',
                S.current.chat_room, Colors.black, () {
              context.push(
                AppPaths.chatRoomPage,
                extra: {
                  'order_id': '${orderDetailData.id}',
                  'name': orderDetailData.restaurantName,
                },
              );
            }),
          if ((orderDetailData.state ?? 0) != 1 ||
              ((orderDetailData.expired ?? 0) == 1 &&
                  (orderDetailData.state ?? 0) == 1))
            _featureItem(IconFont.xiadan, '', S.current.buy_again, Colors.black,
                () {
              context.push(
                AppPaths.restaurantDetailPage,
                extra: {
                  'restaurantId': orderDetailData.restaurantId ?? 0,
                  'buildingId': orderDetailData.buildingId ?? 0,
                  'comeAgainItems':
                      comeAgainItems.map((e) => e.toJson()).toList(),
                },
              );
            }),
          if ((orderDetailData.state ?? 0) == 7 &&
              (orderDetailData.isCommented ?? false) == false)
            _featureItem(IconFont.commentW, '', S.current.give_comment,
                AppColors.baseGreenColor, () {
              context.push(
                AppPaths.orderEvaluatePage,
                extra: {
                  'orderId': orderDetailData.id,
                  'fromPage': 'order',
                },
              );
            }),
          if ((orderDetailData.state ?? 0) == 3)
            _featureItem(
                IconFont.quxiaodingdan, '', S.current.cancel, Colors.black, () {
              AnimateConfirmDialog(context, MediaQuery.of(context).size.width,
                  S.current.do_you_cancel_this_order, () {
                Navigator.pop(context);
                // 使用订单详情控制器取消订单
                final controller = ref.read(
                    myOrderDetailControllerProvider(orderDetailData.id ?? 0)
                        .notifier);
                controller.cancelOrder(orderDetailData.id ?? 0);
              });
            }),
          if ((orderDetailData.state ?? 0) == 1 &&
              (orderDetailData.expired ?? 0) == 0)
            _featureItem(Icons.group, 'assets/images/agentpays.png',
                S.current.agent_pay, Colors.black, () {
              context.push(
                AppPaths.agentPay,
                extra: {
                  'orderId': orderDetailData.id,
                },
              );
            }),
          if ((orderDetailData.state ?? 0) == 1 &&
              (orderDetailData.expired ?? 0) == 0)
            _featureItem(IconFont.pay, '', S.current.i_pay,
                AppColors.restaurantBgStartColor, () {
              // 将响应数据转换为Map
              final Map<String, dynamic> orderDataMap = {
                'orderId': orderDetailData.id,
                'state': orderDetailData.state,
                'totalPrice': orderDetailData.orderDetail?.price ?? 0,
                'originalPrice':
                    orderDetailData.orderDetail?.originalPrice ?? 0,
              };
              // 如果成功获取订单信息，则跳转至支付页面
              ref.context.push(AppPaths.paymentPage, extra: orderDataMap);
            }),
          // if (orderDetailData.shipper != null)
          //   _featureItem(IconFont.dashang, '', S.current.gift, Colors.black,
          //       () {
          //     context.push(
          //       AppPaths.shipperDetailPage,
          //       extra: {
          //         'shipperId': orderDetailData.shipperId,
          //         'orderId': orderDetailData.id,
          //       },
          //     );
          //   }),
        ],
      ),
    );
  }

  Widget _featureItem(IconData icon, String img, String label, Color thisColor,
      Function onClick) {
    return Builder(builder: (context) {
      return InkWell(
        onTap: () {
          onClick.call();
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 15.w),
          alignment: Alignment.center,
          width: (MediaQuery.of(context).size.width - 20.w) / 4,
          child: Column(
            children: [
              img != ''
                  ? Image.asset(
                      img,
                      fit: BoxFit.fill,
                      width: 32.w,
                      height: 32.w,
                    )
                  : Icon(
                      icon,
                      size: 32.sp,
                      color: thisColor,
                    ),
              SizedBox(
                height: 8.w,
              ),
              Text(
                label,
                style: TextStyle(
                  fontSize: mainSize,
                  color: thisColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}

Future<String> showMobileListBottomPanel(
    BuildContext context, OrderDetailData orderDetailData) {
  return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      useSafeArea: true,
      builder: (BuildContext bottomPanelContext) {
        return MobileListBottomPanelWidget(
          orderDetailData: orderDetailData,
        );
      }).then((value) {
    return value ?? '';
  });
}

class MobileListBottomPanelWidget extends ConsumerWidget {
  final OrderDetailData orderDetailData;

  const MobileListBottomPanelWidget({
    Key? key,
    required this.orderDetailData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 获取OrderDetailController
    final controller = ref.read(
        myOrderDetailControllerProvider(orderDetailData.id ?? 0).notifier);

    return SafeArea(
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.baseBackgroundColor,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(children: [
              if ((orderDetailData.adminPhone ?? '') != '')
                InkWell(
                  onTap: () async {
                    // 使用我们的控制器拨打电话
                    controller.makePhoneCall(orderDetailData.adminPhone ?? '');
                  },
                  child: Container(
                    color: Colors.white,
                    padding: EdgeInsets.all(15.w),
                    alignment: Alignment.center,
                    child: Text(
                      S.current.connect_to_shipper,
                      style: TextStyle(fontSize: mainSize),
                    ),
                  ),
                ),
              Divider(color: AppColors.searchBackColor, height: 1),
              InkWell(
                onTap: () async {
                  // 使用我们的控制器拨打电话
                  controller
                      .makePhoneCall(orderDetailData.restaurantPhone ?? '');
                },
                child: Container(
                  color: Colors.white,
                  padding: EdgeInsets.all(15.w),
                  alignment: Alignment.center,
                  child: Text(
                    S.current.connect_to_restaurant,
                    style: TextStyle(fontSize: mainSize),
                  ),
                ),
              ),
              Divider(color: AppColors.searchBackColor, height: 1),
              InkWell(
                onTap: () async {
                  // 使用我们的控制器拨打电话
                  controller.makePhoneCall(orderDetailData.servicePhone ?? '');
                },
                child: Container(
                  color: Colors.white,
                  padding: EdgeInsets.all(15.w),
                  alignment: Alignment.center,
                  child: Text(
                    S.current.connect_to_service,
                    style: TextStyle(fontSize: mainSize),
                  ),
                ),
              ),
              SizedBox(
                height: 15.w,
              ),
            ]),
            InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                color: Colors.white,
                padding: EdgeInsets.all(15.w),
                alignment: Alignment.center,
                child: Text(
                  S.current.cancel,
                  style: TextStyle(fontSize: mainSize),
                ),
              ),
            ),
            SizedBox(
              height: 15.w,
            ),
          ],
        ),
      ),
    );
  }
}
