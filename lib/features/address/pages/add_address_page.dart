import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/address/pages/positioning_page.dart';
import 'package:user_app/features/address/providers/add_address_provider.dart';
import 'package:user_app/features/address/providers/my_address_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';

class AddAddressPage extends ConsumerStatefulWidget {
  AddAddressPage({
    super.key,
    required this.buildingName,
    required this.buildingNameZh,
    required this.address,
    required this.buildingId,
    required this.name,
    required this.tel,
    required this.addressId,
    required this.areaName,
  });
  String buildingName;
  String buildingNameZh;
  String address;
  String name;
  String tel;
  int buildingId;
  int addressId;
  String areaName;

  @override
  ConsumerState createState() => _AddAddressPageState();
}

class _AddAddressPageState extends ConsumerState<AddAddressPage> {
  final TextEditingController _fullAddr = TextEditingController();
  final TextEditingController _name = TextEditingController();
  final TextEditingController _mobile = TextEditingController();
  String? buildingName;
  String? buildingNameZh;
  String? address;
  String? name;
  String? tel;
  int? buildingId;
  int? addressId;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    buildingName = widget.buildingName;
    buildingNameZh = widget.buildingNameZh;
    address = widget.address;
    name = widget.name;
    tel = widget.tel;
    buildingId = widget.buildingId;
    addressId = widget.addressId;

    // 如果buildingId为空，则获取最新位置
    if ((buildingName == null ||
        buildingName == '' ||
        buildingNameZh == null &&
        buildingNameZh == '') ||
        buildingId == null ||
        buildingId == 0) {
      final locationData = ref.read(homeNoticeProvider).value?.location;
      buildingName = locationData?.buildingName ?? '';
      buildingNameZh = locationData?.buildingNameZh ?? '';
      buildingId = locationData?.id ?? 0;
    }

    // if (address == null || address == '') {
    //   final locationData = LocationUtil.getLatestLocationData();
    //   if (locationData != null && (buildingName == null || buildingName == '')) {
    //     // address = locationData['address'] ?? '';
    //     buildingName = locationData['description'] ?? '';
    //     buildingNameZh = locationData['description'] ?? '';
    //   }
    // }

    _fullAddr.text = address ?? '';
    _name.text = name ?? '';
    _mobile.text = tel ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          S.current.get_address,
          style: TextStyle(fontSize: soBigSize),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.w),
                color: Colors.white,
              ),
              child: Column(
                children: [
                  InkWell(
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 10.w,
                        vertical: 15.w,
                      ),
                      child: Column(
                        children: [
                          ref.read(languageProvider) == 'ug'
                              ? Directionality(
                                  textDirection: TextDirection.rtl,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          Text(
                                            'تاللىغان ئورنىڭىز:',
                                            style: TextStyle(
                                              fontSize: mainSize,
                                            ),
                                          ),
                                          Container(
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width -
                                                150.w,
                                            child: Text(
                                              buildingName?.isEmpty ?? true
                                                  ? 'شەھەر /رايوننى تاللاڭ'
                                                  : buildingName!,
                                              style: TextStyle(
                                                fontSize: mainSize,
                                                color: AppColors.baseGreenColor,
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                      Icon(
                                        Icons.arrow_forward_ios,
                                        color: Colors.grey,
                                        size: mainSize,
                                      ),
                                    ],
                                  ),
                                )
                              : Directionality(
                                  textDirection: TextDirection.ltr,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          Text(
                                            '已选择位置：',
                                            style: TextStyle(
                                              fontSize: mainSize,
                                            ),
                                          ),
                                          Text(
                                            buildingNameZh?.isEmpty ?? true
                                                ? '请选择城市/地区'
                                                : buildingNameZh!,
                                            style: TextStyle(
                                              fontSize: mainSize,
                                              color: AppColors.baseGreenColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                      Icon(
                                        Icons.arrow_forward_ios,
                                        color: Colors.grey,
                                        size: mainSize,
                                      ),
                                    ],
                                  ),
                                ),
                        ],
                      ),
                    ),
                    onTap: () async {
                      String latitude =
                          await ref.watch(locationInfoProvider).latitude ??
                              '43.76832775608891';
                      String longitude =
                          await ref.watch(locationInfoProvider).longitude ??
                              '87.62604277173614';
                      String address =
                          await ref.watch(locationInfoProvider).address ??
                              '新疆维吾尔自治区乌鲁木齐市天山区延安路544号靠近瑞达世贸大厦';
                      String name =
                          await ref.watch(locationInfoProvider).description ??
                              '在瑞达世贸大厦附近';

                      print('latitude $latitude');
                      print('longitude $longitude');
                      print('address $address');
                      print('name $name');

                      Navigator.of(context)
                          .push(
                        MaterialPageRoute(
                          builder: (context) => PositioningPage(
                            initialLatitude: num.parse(latitude),
                            initialLongitude: num.parse(longitude),
                            name: name,
                            areaName: widget.areaName,
                            address: address,
                          ),
                        ),
                      )
                          .then((value) async {
                        if (value == null) return;
                        Map<String, dynamic> positionParam =
                            value as Map<String, dynamic>;
                        print(
                          'poiModel ${positionParam['building_id']} ${positionParam['building_name']}  ${positionParam['building_name_zh']}',
                        );
                        buildingId = positionParam['building_id'];
                        buildingName = positionParam['building_name'] ?? '';
                        buildingNameZh =
                            positionParam['building_name_zh'] ?? '';
                        setState(() {});
                      });
                    },
                  ),
                  Divider(height: 1.w, color: AppColors.searchBackColor),
                  Container(
                    height: 90.w,
                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                    child: TextField(
                      controller: _fullAddr,
                      maxLines: 5, // 设置最大行数
                      minLines: 3, // 设置最小行数
                      decoration: InputDecoration(
                        hintText: S.current.input_address_detail, // 占位符
                        hintStyle: TextStyle(
                          fontSize: mainSize,
                          color: AppColors.textSecondColor,
                        ),
                        border: InputBorder.none, // 取消默认边框
                        enabledBorder: InputBorder.none, // 取消非聚焦状态下的边框
                        focusedBorder: InputBorder.none, // 取消聚焦状态下的边框
                      ),
                    ),
                  ),
                  Divider(height: 1.w, color: AppColors.searchBackColor),
                  Container(
                    height: 60.w,
                    padding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.w),
                    // color: Colors.orange,
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 90.w,
                          child: Text(
                            S.current.get_user_name,
                            style: TextStyle(fontSize: mainSize),
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            // focusNode: _keyWordNode,
                            maxLength: 32,
                            controller: _name,
                            // textAlignVertical: TextAlignVertical.center, // 光标居中
                            onChanged: (text) async {
                              // 这里的 text 是输入框中的当前文本
                              // await getSearchByWord(keyWord: text);
                            },
                            cursorColor: Colors.black, // 设置光标颜色为红色
                            style: TextStyle(fontSize: 16.sp),
                            decoration: InputDecoration(
                              border: InputBorder.none, // 去掉底部横线
                              // contentPadding: EdgeInsets.symmetric(vertical: 12.w),  // 调整上下的间距
                              hintText: S.current.input,
                              counterText: '', // 若不想显示计数器，可将此属性设为空字符串
                              hintStyle: TextStyle(
                                color: AppColors.textSecondColor,
                                fontSize: 16.sp,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Divider(height: 1.w, color: AppColors.searchBackColor),
                  Container(
                    height: 60.w,
                    padding: EdgeInsets.symmetric(horizontal: 10.w),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 90.w,
                          child: Text(
                            S.current.get_user_phone,
                            style: TextStyle(fontSize: mainSize),
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            // focusNode: _keyWordNode,
                            controller: _mobile,
                            maxLength: 11,
                            // textAlignVertical: TextAlignVertical.center, // 光标居中
                            onChanged: (text) async {
                              // 这里的 text 是输入框中的当前文本
                              // await getSearchByWord(keyWord: text);
                            },
                            keyboardType: TextInputType.number,
                            textInputAction: TextInputAction.done,
                            cursorColor: Colors.black, // 设置光标颜色为红色
                            style: TextStyle(fontSize: 16.sp),
                            decoration: InputDecoration(
                              border: InputBorder.none, // 去掉底部横线
                              // contentPadding: EdgeInsets.symmetric(vertical: 12.w),  // 调整上下的间距
                              counterText: '', // 若不想显示计数器，可将此属性设为空字符串
                              hintText: S.current.input,
                              hintStyle: TextStyle(
                                color: AppColors.textSecondColor,
                                fontSize: 16.sp,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            InkWell(
              onTap: () async {
                String name = _name.text;
                String tel = _mobile.text;
                String address = _fullAddr.text;
                String buildingId = this.buildingId.toString();

                if (!validateNotEmpty(address)) {
                  BotToast.showText(
                    text: S.current.address_no_have_to_be_null,
                  );
                  return;
                }

                if (!validateNotEmpty(name)) {
                  BotToast.showText(text: S.current.name_no_have_to_be_null);
                  return;
                }

                if (!validatePhoneNumber(tel)) {
                  BotToast.showText(
                    text: S.current.mobile_no_have_to_be_null,
                  );
                  return;
                }

                if (addressId != 0) {
                  Map<String, dynamic> param = {
                    'name': name,
                    'tel': tel,
                    'address': address,
                    'building_id': buildingId,
                    'address_id': addressId,
                  };
                  await ref
                      .read(addressListProvider.notifier)
                      .updateAddress(param);
                } else {
                  await ref.read(addAddressProvider.notifier).addAddressData(
                        name: name,
                        tel: tel,
                        address: address,
                        buildingId: buildingId,
                      );
                }
                Navigator.of(context).pop();
              },
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6.w),
                  color: AppColors.baseGreenColor,
                ),
                alignment: Alignment.center,
                padding: EdgeInsets.symmetric(vertical: 15.w),
                margin: EdgeInsets.all(20.w),
                child: Text(
                  S.current.save_confirm,
                  style: TextStyle(color: Colors.white, fontSize: soBigSize),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 验证是否空
  bool validateNotEmpty(final String val) {
    return val.isNotEmpty;
  }

  /// 验证手机号
  bool validatePhoneNumber(final String phone) {
    return phone.isNotEmpty && phone.length == 11;
  }
}
