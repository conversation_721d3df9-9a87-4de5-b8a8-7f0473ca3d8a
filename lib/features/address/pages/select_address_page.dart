import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/icon_font.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/address/city_area_model.dart';
import 'package:user_app/data/models/address/street_list_model.dart';
import 'package:user_app/features/address/pages/connect_page.dart';
import 'package:user_app/features/address/providers/address_city_provider.dart';
import 'package:user_app/features/address/providers/address_park_provider.dart';
import 'package:user_app/features/address/providers/address_street_provider.dart';
import 'package:user_app/generated/l10n.dart';

class SelectAddressPage extends ConsumerStatefulWidget {
  const SelectAddressPage({super.key});

  @override
  ConsumerState createState() => _SelectAddressPageState();
}

class _SelectAddressPageState extends ConsumerState<SelectAddressPage> {
  int areaId = 0;
  bool searched = false;
  List<StreetListData> streetList = [];
  final TextEditingController _keyWord = TextEditingController();

  @override
  void initState() {
    // TODO: implement initState
    SchedulerBinding.instance.addPostFrameCallback((final timeStamp) {
      ref.read(addressCityProvider.notifier).fetchAddressData();
    });
    super.initState();
  }

  List<StreetListData> searchFunc(List<StreetListData> streetList, String val) {
    List<StreetListData> resultList = [];
    for (int i = 0; i < streetList.length; i++) {
      if (streetList[i].name!.contains(val)) {
        resultList.add(streetList[i]);
      }
    }
    return resultList;
  }

  @override
  Widget build(BuildContext context) {
    ref.watch(addressCityProvider);
    ref.watch(addressStreetProvider);
    ref.watch(addressParkProvider);
    ref.watch(selectedAddressProvider);
    return Scaffold(
      backgroundColor: AppColors.baseBackgroundColor,
      appBar: AppBar(
        title: Text(
          S.current.select_address,
          style: TextStyle(fontSize: soBigSize),
        ),
      ),
      body: Column(
        children: [
          InkWell(
            onTap: () {
              // partList.clear();
              // partList.add(0);
              // partList.add(0);
              // setState(() {});
            },
            child: Container(
              alignment: Alignment.center,
              height: 56.w,
              color: Colors.white,
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        if (ref.read(selectedAddressProvider).isNotEmpty) {
                          ref.read(selectedAddressProvider.notifier).state = [];
                        }
                      },
                      child: Container(
                        alignment: Alignment.center,
                        child: Text(
                          ref.watch(selectedAddressProvider).isNotEmpty
                              ? ref.watch(selectedAddressProvider)[0]
                              : S.current.select_area,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: titleSize,
                          ),
                        ),
                      ),
                    ),
                  ),
                  // 如果 _isExpanded 为 true，则显示新的 Container
                  if (ref.watch(selectedAddressProvider).isNotEmpty)
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          if (ref.read(selectedAddressProvider).length > 1) {
                            ref
                                .read(selectedAddressProvider.notifier)
                                .state
                                .removeLast();
                            ref
                                .read(addressStreetProvider.notifier)
                                .fetchAddressData(areaId: areaId);
                          }
                        },
                        child: Container(
                          height: 56.w,
                          alignment: Alignment.center,
                          decoration:
                              ref.watch(selectedAddressProvider).length > 1
                                  ? BoxDecoration()
                                  : BoxDecoration(
                                      border: Border(
                                        bottom: BorderSide(
                                          width: 1.w,
                                          color: Color(0xffe5e5e5),
                                        ),
                                      ),
                                    ),
                          child: Text(
                            ref.watch(selectedAddressProvider).length > 1
                                ? ref.watch(selectedAddressProvider)[1]
                                : S.current.select_street,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: titleSize,
                            ),
                          ),
                        ),
                      ),
                    ),

                  if (ref.watch(selectedAddressProvider).length > 1)
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          if (ref.read(selectedAddressProvider).length > 2) {
                            ref
                                .read(selectedAddressProvider.notifier)
                                .state
                                .removeLast();
                            // setState(() {});
                          }
                        },
                        child: Container(
                          height: 56.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                width: 1.w,
                                color: Color(0xffe5e5e5),
                              ),
                            ),
                          ),
                          child: Text(
                            ref.watch(selectedAddressProvider).length > 2
                                ? ref.watch(selectedAddressProvider)[2]
                                : S.current.select_park,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: titleSize,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
          // SizedBox(
          //   height: 1.w,
          // ),
          if (ref.watch(selectedAddressProvider).isEmpty) _areaPart(),
          if (ref.watch(selectedAddressProvider).isNotEmpty) _searchPanel(),
          if (ref.watch(selectedAddressProvider).isNotEmpty) _streetPart(),
        ],
      ),
    );
  }

  Widget _areaPart() {
    return Consumer(
      builder: (final context, final ref, final child) {
        return ref.watch(addressCityProvider).when(
          data: (data) {
            return Expanded(
              // height: MediaQuery.of(context).size.height - 80.w,
              child: SingleChildScrollView(
                child: Column(
                  children: List.generate(
                    (data ?? []).length,
                    (index) =>
                        _areaItem(ref.watch(addressCityProvider).value![index]),
                  ),
                ),
              ),
            );
          },
          loading: () {
            // 正在加载，显示加载指示器
            return Expanded(child: Center(child: LoadingWidget()));
          },
          error: (error, stackTrace) {
            // 数据加载失败，显示错误信息
            return Text('Search Page Error: $error');
          },
        );
      },
    );
  }

  Widget _areaItem(CityAreaData cityData) {
    return Container(
      padding:
          EdgeInsets.only(right: 15.0.w, left: 15.w, bottom: 20.w, top: 5.w),
      margin: EdgeInsets.only(bottom: 10.w),
      color: Colors.white,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 15.w),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${cityData.name}',
                  style: TextStyle(
                    color: AppColors.textSecondColor,
                    fontSize: titleSize,
                  ),
                ),
                SizedBox(),
              ],
            ),
          ),
          GridView.count(
            // Create a grid with 2 columns. If you change the scrollDirection to
            // horizontal, this produces 2 rows.
            shrinkWrap:
                true, // Allow GridView to take up only the space it needs
            physics: NeverScrollableScrollPhysics(),
            crossAxisCount: 3,
            mainAxisSpacing: 15.w,
            crossAxisSpacing: 15.w,
            childAspectRatio: 2.6,
            // Generate 100 widgets that display their index in the List.
            children: List.generate((cityData.area ?? []).length, (areaIndex) {
              return InkWell(
                onTap: () async {

                  debugPrint('大区选择: ${cityData.area?[areaIndex].name}');
                  // 判断是否已经添加，如果已添加则直接返回
                  if (ref
                      .read(selectedAddressProvider)
                      .contains(cityData.area?[areaIndex].name ?? '')) {
                    return;
                  }
                  if((cityData.area?[areaIndex].state ?? 0) == 1){
                    ref
                        .read(selectedAddressProvider.notifier)
                        .state
                        .add(cityData.area?[areaIndex].name ?? '');
                    ref.read(addressStreetProvider.notifier).fetchAddressData(
                          areaId: cityData.area?[areaIndex].id ?? 0,
                        );
                    areaId = cityData.area?[areaIndex].id ?? 0;
                  }else{
                    Navigator.of(AppContext().currentContext!).push(MaterialPageRoute(builder: (context) => ConnectPage(name: cityData.area?[areaIndex].name ?? '',),));
                  }



                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5.w),
                    color: AppColors.baseBackgroundColor,
                  ),
                  // margin: EdgeInsets.all(5.0),
                  child: Center(
                    child: Text(
                      cityData.area?[areaIndex].name ?? '',
                      style: TextStyle(fontSize: mainSize, color: Colors.black),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _searchPanel() {
    List<StreetListData> streetList = [];
    for (int i = 0; i < ref.watch(newStreetProvider).length; i++) {
      for (int j = 0;
          j < ref.watch(newStreetProvider)[i]['items'].length;
          j++) {
        StreetListData streetListData =
            ref.watch(newStreetProvider)[i]['items'][j];
        streetList.add(streetListData);
      }
    }
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: 50.w,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.w),
          color: Colors.red,
        ),
        margin: EdgeInsets.symmetric(vertical: 10.w),
        child: TextField(
          // focusNode: _keyWordNode,
          controller: _keyWord,
          style: TextStyle(fontSize: 15.sp),
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.all(10.0),
            hoverColor: AppColors.baseBackgroundColor,
            fillColor: AppColors.baseBackgroundColor,
            filled: true,
            hintText: ref.read(selectedAddressProvider).length == 2
                ? S.current.input_park
                : S.current.input_street,
            hintStyle:
                TextStyle(color: AppColors.textSecondColor, fontSize: mainSize),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: AppColors.baseBackgroundColor,
              ),
            ),
            prefixIcon: Icon(
              IconFont.search,
              size: 26.w,
              color: AppColors.textSecondColor,
            ),
            focusedBorder: OutlineInputBorder(
              //选中时外边框颜色
              borderSide: BorderSide(
                color: AppColors.baseBackgroundColor,
              ),
            ),
          ),
          maxLines: 1, //不限制行数
          // autofocus: true,
          textInputAction: TextInputAction.next,
          keyboardType: TextInputType.text,
          onChanged: (val) async {
            print('val---1-->$val');

            if (val.trim() != '') {
              searched = true;
              this.streetList = searchFunc(streetList, val);
            } else {
              searched = false;
            }
            setState(() {});
            // await getListByInput(keyWord: val);
          },
          // controller: Provider.of<LoginViewmodel>(context).getUser,
        ),
      ),
    );
  }

  Widget _streetPart() {
    return searched
        ? _searchResult(this.streetList)
        : Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: List.generate(
                  ref.watch(newStreetProvider).length,
                  (index) => _streetItem(index),
                ),
              ),
            ),
          );
  }

  Widget _streetItem(int index) {
    List<StreetListData> streetList = [];
    for (int i = 0;
        i < ref.watch(newStreetProvider)[index]['items'].length;
        i++) {
      StreetListData streetListData =
          ref.watch(newStreetProvider)[index]['items'][i];
      streetList.add(streetListData);
    }

    return Container(
      alignment: Alignment.center,
      margin: EdgeInsets.only(bottom: 10.w),
      // borderRadius: BorderRadius.circular(10.px),
      color: Colors.white,
      child: Column(
        children: [
          SizedBox(
            height: 15.w,
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Text(
                  ref.watch(newStreetProvider)[index]['char'],
                  style: TextStyle(
                    color: AppColors.textSecondColor,
                    fontSize: titleSize,
                  ),
                ),
                SizedBox(),
              ],
            ),
          ),
          SizedBox(
            height: 5.w,
          ),
          Container(
            child: Column(
              children: List.generate(
                streetList.length,
                (innerIndex) => _streetNameItem(
                  streetList[innerIndex],
                  innerIndex,
                  streetList.length,
                ),
              ),
            ),
          ),
          SizedBox(
            height: 5.w,
          ),
        ],
      ),
    );
  }

  Widget _searchResult(List<StreetListData> streetList) {
    return Expanded(
      child: SingleChildScrollView(
        child: Container(
          color: Colors.white,
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.w),
                child: Row(
                  children: [
                    Text(
                      S.current.search_res,
                      style: TextStyle(
                        color: AppColors.textHintColor,
                        fontSize: titleSize,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 5.w,
              ),
              Container(
                child: Column(
                  children: List.generate(
                    streetList.length,
                    (innerIndex) => _streetNameItem(
                      streetList[innerIndex],
                      innerIndex,
                      streetList.length,
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 5.w,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _streetNameItem(
    StreetListData streetListData,
    int index,
    int listSize,
  ) {
    return InkWell(
      onTap: () async {
        debugPrint('街道选择: ${streetListData.name}');

        // 判断是否已经添加，如果已添加则直接返回
        if (ref.read(selectedAddressProvider).contains(streetListData.name ?? '')) {
          return;
        }
        
        ref
            .read(selectedAddressProvider.notifier)
            .state
            .add(streetListData.name ?? '');

        ref
            .read(addressParkProvider.notifier)
            .fetchAddressData(streetId: streetListData.id ?? 0);

        if (ref.read(selectedAddressProvider).length > 2) {
          Map<String, dynamic> addressParam = {
            'building_id': streetListData.id,
            'building_name': streetListData.name,
            'area_id': areaId,
          };
          Navigator.of(context).pop(addressParam); // 返回并传递参数
        }
        _keyWord.text = '';
        searched = false;
        setState(() {});
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.w),
        margin: EdgeInsets.symmetric(horizontal: 10.w),
        decoration: index != listSize - 1
            ? BoxDecoration(
                border: Border(
                  bottom: BorderSide(width: 1.w, color: Color(0xffe5e5e5)),
                ),
              )
            : BoxDecoration(),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Container(
              width: MediaQuery.of(context).size.width - 40.w,
              child: Text(
                streetListData.name ?? '',
                style: TextStyle(color: Colors.black, fontSize: titleSize),
              ),
            ),
            SizedBox(),
          ],
        ),
      ),
    );
  }
}
