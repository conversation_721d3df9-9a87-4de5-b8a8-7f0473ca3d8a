import 'package:bot_toast/bot_toast.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/utils/image_util.dart';
import 'package:user_app/core/utils/wechat_util.dart';
import 'package:user_app/core/widgets/dialogs/loading_dialog.dart';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/data/repositories/user/user_repository.dart';
import 'package:user_app/features/auth/providers/auth_provider.dart';
import 'package:user_app/features/mine/pages/mine/mine_state.dart';
import 'package:user_app/features/mine/services/mine_service.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/main.dart';
import 'package:user_app/routes/paths.dart';
import 'package:user_app/routes/app_router.dart';

part 'mine_controller_provider.g.dart';

/// 个人中心控制器
@riverpod
class MineController extends _$MineController {
  @override
  MineState build() {
    // 初始化时获取数据
    Future.microtask(() {
      init();
    });

    ref.watch(languageProvider);

    // 监听登录状态变化
    ref.listen(isLoggedInProvider, (final previous, final next) {
      refreshUserData();
    });

    return const MineState();
  }

  Future<void> init() async {
    fetchWebList();
    refreshUserData();
    getServicePhone();
    getAppVersion();
  }

  /// 获取应用版本号
  Future<void> getAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final currentPatchNumber =
          ref.read(localStorageRepositoryProvider).getCurrentPatchNumber();
      state = state.copyWith(
        version: '${packageInfo.version}(${packageInfo.buildNumber})',
        patchNumber: currentPatchNumber,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 获取网页列表
  Future<void> fetchWebList() async {
    state = state.copyWith(isLoading: true);
    try {
      final webList =
          await ref.read(mineServiceProvider.notifier).fetchWebList();
      state = state.copyWith(
        webList: webList,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 打开网页
  /// [url] 网页地址
  /// [title] 网页标题
  void openWebPage(final String url, final String title) {
    router.push(AppPaths.webViewPage, extra: {
      'url': url,
      'title': title,
    });
  }

  /// 打开代理资质页面
  void openLicenseType() {
    router.push(AppPaths.licensePage);
  }

  /// 打开如何下单页面
  void openHowToOrder() {
    router.push(AppPaths.videoPage);
  }

  /// 打开帮助页面
  void openHelp() {
    router.push(AppPaths.helpPage);
  }

  /// 打开用户协议页面
  void openUserAgreement() {
    router.push(
      AppPaths.webViewPage,
      extra: {
        'url': UrlConstants.userAgreementUrl,
        'title': S.current.about_info_syxy,
      },
    );
  }

  /// 打开关于页面
  void openAboutInfo() {
    String lang = globalContainer.read(languageProvider);
    final url = 'https://smart.mulazim.com/$lang/v1/about/detail?type=1';
    router.push(
      AppPaths.webViewPage,
      extra: {
        'url': url,
        'title': S.current.about_info,
      },
    );
  }

  /// 拨打电话
  /// [phoneNumber] 电话号码
  Future<void> makePhoneCall(final String phoneNumber) async {
    await ref.read(mineServiceProvider.notifier).makePhoneCall(phoneNumber);
  }

  /// 退出登录
  Future<void> logout() async {
    try {
      await ref.read(authProvider.notifier).logout();
      state = state.copyWith(userInfo: null);
      // if (ref.context != null) {
      //   MainPageTabs.navigateToTab(ref.context!, MainPageTabs.home);
      // }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 退出登录
  Future<void> unRegisterJPush() async {
    try {
      await ref.read(authProvider.notifier).logout();
      state = state.copyWith(userInfo: null);
      // if (ref.context != null) {
      //   MainPageTabs.navigateToTab(ref.context!, MainPageTabs.home);
      // }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 刷新用户数据
  Future<void> refreshUserData() async {
    try {
      final userInfo = await ref.read(userRepositoryProvider).getUserInfo();
      state = state.copyWith(userInfo: userInfo);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 获取客服电话
  Future<void> getServicePhone() async {
    try {
      final phone =
          await ref.read(mineServiceProvider.notifier).getServicePhone();
      state = state.copyWith(servicePhone: phone);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 打开客服页面
  Future<void> openCustomerService() async {
    final result = await WechatUtil().openCustomerServiceChat();
    if (!result) {
      BotToast.showText(text: S.current.open_costumet_faield);
    }
  }

  /// 分享应用
  Future<void> shareApp() async {
    LoadingDialog().show();
    final result = await WechatUtil().shareMiniProgram(
      thumbData: await ImageUtil.getImageFromUrl(
        'https://cdns.mulazim.com/wechat_mini/share.jpg',
      ),
      path: "pages/index/index",
      title: S.current.app_description,
      description: S.current.app_description,
    );
    if (result) {
      BotToast.showText(text: S.current.about_share_success);
    } else {
      BotToast.showText(text: S.current.about_share_failed);
    }
    LoadingDialog().hide();
  }
}
