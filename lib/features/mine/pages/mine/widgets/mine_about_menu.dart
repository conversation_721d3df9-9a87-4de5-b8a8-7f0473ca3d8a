import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/utils/toast_util.dart';
import 'package:user_app/features/mine/pages/mine/mine_controller_provider.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_menu_item.dart';
import 'package:user_app/features/mine/pages/mine/widgets/mine_menu_section.dart';
import 'package:user_app/generated/l10n.dart';
import 'dart:async';

/// 关于信息菜单区域
class MineAboutMenu extends ConsumerStatefulWidget {
  /// 构造函数
  const MineAboutMenu({super.key});

  @override
  ConsumerState<MineAboutMenu> createState() => _MineAboutMenuState();
}

class _MineAboutMenuState extends ConsumerState<MineAboutMenu> {
  int _clickCount = 0;
  Timer? _resetTimer;

  @override
  void dispose() {
    _resetTimer?.cancel();
    super.dispose();
  }

  void _onVersionTap() {
    // 取消之前的定时器
    _resetTimer?.cancel();

    // 增加点击次数
    _clickCount++;

    // 如果达到5次点击，跳转到URL配置页面
    if (_clickCount >= 5) {
      // context.push(AppPaths.urlConfigPage);  //以后再做
      // _clickCount = 0; // 重置计数
      return;
    }

    // 设置1秒后重置计数
    _resetTimer = Timer(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _clickCount = 0;
        });
      }
    });
  }

  @override
  Widget build(final BuildContext context) {
    return MineMenuSection(
      children: [
        MineMenuItem(
          iconPath: 'assets/images/mine/xieyi.png',
          title: S.current.about_info,
          showDivider: true,
          onTap: () =>
              ref.read(mineControllerProvider.notifier).openAboutInfo(),
        ),
        // 版本信息
        GestureDetector(
          onLongPress: () {
            ToastUtil.showBottom(
              'P${ref.read(mineControllerProvider).patchNumber}',
            );
          },
          child: MineMenuItem(
            iconPath: 'assets/images/mine/info.png',
            title: S.current.version,
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Consumer(
                  builder: (final context, final ref, final child) {
                    final version = ref.watch(mineControllerProvider).version;
                    return Text(
                      'V$version',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondColor,
                      ),
                    );
                  },
                ),
              ],
            ),
            onTap: _onVersionTap,
          ),
        ),
      ],
    );
  }
}
