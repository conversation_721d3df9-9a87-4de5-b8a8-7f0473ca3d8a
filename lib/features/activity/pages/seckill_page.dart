import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/utils/format_util.dart';
import 'package:user_app/core/widgets/loading_widget.dart';
import 'package:user_app/data/models/activity/seckill_food_model.dart';
import 'package:user_app/features/activity/providers/seckill_list_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';

class SeckillPage extends ConsumerStatefulWidget {
  SeckillPage({super.key,required this.buildingId});
  int buildingId;

  @override
  ConsumerState createState() => _SeckillPageState();
}

class _SeckillPageState extends ConsumerState<SeckillPage> with TickerProviderStateMixin {

  int currentTimeIndex = 0;
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    
    // 初始化进度条动画控制器
    _progressController = AnimationController(
      duration: const Duration(seconds: 2), // 2秒动画
      vsync: this,
    );
    
    // 创建从0到0.6的动画（60%）
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 0.6,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
    
    // 启动动画
    _progressController.forward();
    
    WidgetsBinding.instance.addPostFrameCallback((final timeStamp) async {
      ref.read(seckillListProvider.notifier).fetchSecKillData(buildingId: widget.buildingId);
    });
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.baseGreenColor,
        foregroundColor: Colors.white,
        centerTitle: true,
        title: Text(S.current.sec_kill,style: TextStyle(fontSize: soBigSize,color: Colors.white),),
      ),
      body: ref.watch(seckillListProvider).when(data: (final data){
        // 使用 WidgetsBinding.instance.addPostFrameCallback 避免在 build 过程中直接调用 ref
        WidgetsBinding.instance.addPostFrameCallback((final _) {
          if (mounted) {
            Future.delayed(Duration(seconds: data?.refreshSecond ?? 600), () {
              if (mounted) {
                ref.read(seckillListProvider.notifier).fetchSecKillData(buildingId: widget.buildingId);
              }
            });
          }
        });
        
        return Column(
          children: [
            Container(
              color: AppColors.baseGreenColor,
              alignment: ref.watch(languageProvider) == 'ug' ? Alignment.centerRight : Alignment.centerLeft,
              child: SingleChildScrollView(
                scrollDirection:Axis.horizontal,
                child: Row(
                  children: List.generate((data?.times ?? []).length, (final index)=>_timeItem(data!.times![index],index)),
                ),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  margin: EdgeInsets.only(bottom: 10.w),
                  child: Column(
                    children: List.generate((data?.foods ?? []).length, (final index)=>_foodItem(data!.foods![index])),
                  ),
                ),
              ),
            )
          ],
        );
      }, error: (final error, final stackTrace) {
        print('stackTrace $stackTrace'); 

        // 数据加载失败，显示错误信息
        return Center(child: Column(
          children: [
            Text('address Page Error: $error'),
            Text('address Page stackTrace: $stackTrace'),
          ],
        ));
      }, loading: () {
        // 正在加载，显示加载指示器
        return Center(child: LoadingWidget(),);
      },),
    );
  }

  Widget _timeItem(final SecKillFoodTimes time,final int index){
    return InkWell(
      onTap: (){
        ref.read(seckillListProvider.notifier).fetchSecKillData(buildingId: widget.buildingId,beginTime: time.beginTime);
        currentTimeIndex = index;
        setState(() {});
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10.w,horizontal: 20.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(25.w),
              topRight: Radius.circular(25.w),
            ),
            color: index == currentTimeIndex ? AppColors.languageBgColor.withAlpha(150) : AppColors.baseGreenColor),
        child: Column(
          children: [
            Text('${time.beginTime?.substring(11,16)}',style: TextStyle(fontSize: 24.sp,color: Colors.white,fontWeight: FontWeight.bold,fontFamily: 'NumberFont',)),
            Text('${time.isToday == 1 ? (time.active == 1 ? S.current.sec_killing : S.current.not_begin) : time.beginTime?.substring(5,10)}',style: TextStyle(color: Colors.white,fontSize: mainSize,fontWeight: FontWeight.bold),)
          ],
        ),
      ),
    );
  }

  Widget _foodItem(final Foods foods){
    double lineWidth = MediaQuery.of(context).size.width/3.6;
    return InkWell(
      onTap: (){
        context.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': foods.restaurantId ?? 0,
            'buildingId': widget.buildingId,
            'ids': [foods.id ?? 0],
          },
        );
      },
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.w),
            color: Colors.white
        ),
        padding: EdgeInsets.all(10.w),
        margin: EdgeInsets.only(left:10.w,right: 10.w,top: 10.w),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10.w),
              child: PrefectImage(
                imageUrl: foods.image ?? '',
                width: 130.w,
                height: 130.w,
                fit: BoxFit.fitHeight,
              ),
            ),
            SizedBox(width: 10.w,),
            Expanded(
              child: Container(
                child: Column(
                  children: [
                    Container(
                      alignment: ref.watch(languageProvider) == 'ug' ? Alignment.centerRight : Alignment.centerLeft,
                      child:Text(foods.name ?? '',textAlign:TextAlign.start,style: TextStyle(fontSize: titleSize,color: Colors.black),maxLines: 1,overflow: TextOverflow.ellipsis,)
                    ),
                    SizedBox(height: 5.w,),

                    Container(
                        alignment: ref.watch(languageProvider) == 'ug' ? Alignment.centerRight : Alignment.centerLeft,
                        child:Text(foods.restaurantName ?? '',textAlign:TextAlign.start,style: TextStyle(fontSize: mainSize,color: AppColors.textSecondaryColor),maxLines: 1,overflow: TextOverflow.ellipsis,)
                    ),

                    SizedBox(height: 5.w,),

                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(FormatUtil.formatAmount(foods.price ?? 0),style: TextStyle(fontSize: 20.sp,color: Colors.red,fontWeight: FontWeight.bold,fontFamily: 'NumberFont',)),
                        Text('￥',style: TextStyle(fontSize: mainSize,color: Colors.red)),
                        SizedBox(width: 5.w,),

                        Text(FormatUtil.formatAmount(foods.oldPrice ?? 0),  style: TextStyle(fontSize: secondSize,decoration: TextDecoration.lineThrough,color: AppColors.textSecondaryColor),),
                        Text('￥',style: TextStyle(decoration: TextDecoration.lineThrough,fontSize: littleSize,color: AppColors.textSecondaryColor)),
                        SizedBox(width: 5.w,),

                        Text('${S.current.sec_kill_limit}:${foods.maxOrderCount}',style: TextStyle(fontSize: secondSize,color: AppColors.textSecondaryColor)),
                      ],
                    ),
                    SizedBox(height: 5.w,),

                    Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width:lineWidth,
                          child: AnimatedBuilder(
                            animation: _progressAnimation,
                            builder: (context, child) {
                              return CustomLinearProgressIndicator(
                                progress: int.parse((foods.saledCount ?? 0).toString()) / ((foods.totalCount ?? 0)),
                                width: lineWidth,
                                color: AppColors.baseGreenColor,
                                height: 15.w,
                              );
                            },
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 15.w,vertical: 5.w),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(30.w),
                              color: foods.seckillActive == 1 ? AppColors.baseGreenColor : AppColors.textSecondColor.withAlpha(180)
                          ),
                          child: Text(S.current.sec_kill_buy,style: TextStyle(color: Colors.white,fontSize: titleSize),),
                        )

                      ],
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

}

// 自定义线性进度条widget
class CustomLinearProgressIndicator extends StatelessWidget {
  final double progress; // 进度值 (0.0 到 1.0)
  final double width;
  final double height;
  final Color color;

  const CustomLinearProgressIndicator({
    Key? key,
    required this.progress,
    required this.width,
    required this.height,
    required this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(height / 2),
        color: color.withAlpha(35),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(height / 2),
        child: Stack(
          children: [
            // 背景
            Container(
              width: width,
              height: height,
              color: color.withAlpha(35),
            ),
            // 进度条
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: width * progress,
              height: height,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(height / 2),
                gradient: LinearGradient(
                  colors: [
                    color,
                    color.withOpacity(0.8),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
