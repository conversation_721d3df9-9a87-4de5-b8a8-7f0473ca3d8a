import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/config/config.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/core/theme/app_font_size.dart';
import 'package:user_app/core/widgets/prefect_image.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/index.dart';
import 'package:user_app/routes/paths.dart';

class DiscountDialog extends Dialog {
  final String title;
  final String content;
  final double screenWidth;
  Discount? discount;
  int buildingId;
  // 构造函数赋值
  DiscountDialog(
      {Key? key,
      this.title = "",
      this.content = "",
      this.screenWidth = 0.0,
      this.discount,
      required this.buildingId,
      })
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 调用方法
    // _showTimer(context);
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
      child: Material(
        textStyle: TextStyle(
          fontFamily: AppConstants.mainFont, // 添加维语字体
        ),
          type: MaterialType.transparency,
          child: Center(
            child: Consumer(
              builder: (context,ref,child) {
                return Directionality(
                  textDirection: ref.watch(languageProvider) == 'ug' ? TextDirection.rtl : TextDirection.ltr,
                  child: Container(
                    width: screenWidth - 60.w,
                    // height:Adapt.setPx(140),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.w),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Container(
                          // height: 520.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12.w),
                            color: AppColors.baseBackgroundColor,
                          ),
                          child: Column(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    topRight:Radius.circular(12.w),
                                    topLeft:Radius.circular(12.w),
                                  ),
                                  color: AppColors.baseGreenColor,
                                ),
                                alignment: Alignment.center,
                                height: 60.w,
                                child: Text('${discount?.name ?? ''}',style: TextStyle(color: Colors.white,fontSize: soBigSize,fontFamily: 'UkijTuzTom'),),
                              ),
                              Column(
                                children: List.generate((discount?.foods ?? []).length, (index)=>_foodItem(context, discount!.foods![index])),
                              ),
                              SizedBox(height: 15.w,),
                              Container(
                                alignment: Alignment.center,
                                padding: EdgeInsets.symmetric(horizontal: 30.w),
                                child: Text(S.current.waiting_discount_foods,textAlign: TextAlign.center,style: TextStyle(color: AppColors.textSecondaryColor,fontSize: titleSize,fontFamily: 'UkijTuzTom'),),
                              ),
                              SizedBox(height: 12.w,),

                              InkWell(
                                onTap: (){
                                  Navigator.of(context).pop();
                                  MainPageTabs.navigateToTab(context, MainPageTabs.discount);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(30.w),
                                    color: AppColors.baseGreenColor,
                                  ),
                                  padding: EdgeInsets.symmetric(horizontal: 30.w,vertical: 12.w),
                                  margin: EdgeInsets.symmetric(horizontal: 25.w),
                                  child: Text(S.current.see_detail,textAlign: TextAlign.center,style: TextStyle(color: Colors.white,fontSize: soBigSize,fontFamily: 'UkijTuzTom'),),
                                ),
                              ),
                              SizedBox(height: 20.w,),

                            ],
                          ),
                          // child: _swiperPart(popupAdver!),
                        ),
                        SizedBox(
                          height: 20.w,
                        ),
                        InkWell(
                          child: Container(
                              margin: EdgeInsets.only(left: 5.w),
                              alignment: Alignment.center,
                              width: 45.w,
                              height: 45.w,
                              child: Image.asset('assets/images/message_exit.png')),
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          )
      ),
    );
  }

  Widget _foodItem(BuildContext context, DiscountFoods food){
    return InkWell(
      onTap: (){
        router.push(
          AppPaths.restaurantDetailPage,
          extra: {
            'restaurantId': discount?.id ?? 0,
            'buildingId': buildingId,
            'ids': [food.id ?? 0],
          },
        );
        Navigator.of(context).pop();

      },
      child: Container(
        margin: EdgeInsets.only(top:10.w,right: 10.w,left: 10.w),
        padding: EdgeInsets.all(10.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.w),
          color: Colors.white,
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(10.w),
              child: PrefectImage(
                imageUrl: food.image ?? '',
                width: 112.w,
                height: 96.w,
                fit: BoxFit.fill,
              ),
            ),

            SizedBox(width: 10.w,),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('${food.name}',style: TextStyle(color: Colors.black,fontWeight: FontWeight.bold,fontSize: titleSize,fontFamily: 'UkijTuzTom'),maxLines: 1,overflow: TextOverflow.ellipsis,textAlign: TextAlign.start,),
                  SizedBox(height: 10.w,),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text('${food.discountPrice}',style: TextStyle(color: Colors.red,fontWeight: FontWeight.bold,fontSize: 22.sp),),
                      Padding(padding: EdgeInsets.only(bottom: 1.w),child: Text('￥',style: TextStyle(color: Colors.red,fontWeight: FontWeight.bold,fontSize: titleSize),),),
                      SizedBox(width: 10.w,),
                      Text('￥${food.price}',style: TextStyle(decoration: TextDecoration.lineThrough,decorationColor: AppColors.textSecondaryColor,color: AppColors.textSecondaryColor),),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  // List<String> swiperListStr = [
  //   'assets/images/marketing_swiper_pic.png',
  //   'assets/images/marketing_swiper_pic.png',
  //   'assets/images/marketing_swiper_pic.png',
  // ];
}
