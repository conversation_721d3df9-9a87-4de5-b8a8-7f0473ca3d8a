import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:go_router/go_router.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import 'package:user_app/core/app/app_context.dart';
import 'package:user_app/core/config/app_constants.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/models/location/location_model.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/utils/connection_status.dart';
import 'package:user_app/core/storage/storage_service.dart';
import 'package:user_app/core/utils/location_util.dart';
import 'package:user_app/data/models/home/<USER>';
import 'package:user_app/data/repositories/storage/local_storage_repository.dart';
import 'package:user_app/features/address/pages/address_page.dart';
import 'package:user_app/features/home/<USER>/index/home_state.dart';
import 'package:user_app/features/home/<USER>/parts/dialog_part.dart';
import 'package:user_app/features/home/<USER>/home_notice_provider.dart';
import 'package:user_app/features/home/<USER>/home_provider.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/features/home/<USER>/terminal_provider.dart';
import 'package:user_app/routes/paths.dart';

part 'home_controller.g.dart';

/// 添加 ScrollController Provider
final homeScrollControllerProvider =
    StateProvider<ScrollController?>((final ref) => null);

/// 是否去开启权限设置状态
final isOpenPermissionSettingProvider = StateProvider<bool>((final ref) => false);

/// 首页控制器
@riverpod
class HomeController extends _$HomeController {
  bool _isLoadingRestaurantList = false; // 是否正在加载餐厅列表

  @override
  HomeState build() {
    // 添加网络状态监听
    ref.listen(connectionStateProvider, (final previous, final current) {
      log("网络状态变化: ${current ? '已连接' : '已断开'}");
      // 当网络重新连接时，可以尝试重新加载数据
      if (current && previous == false) {
        _reloadDataAfterNetworkReconnect();
      }
    });

    ref.listen(homeNoticeProvider, (final previous, final current) async {
      if (current.value != null) {
        await _loadRecommendDialog();
        await loadCouponDialog();
      }
    });

    // ref.listen(isOpenPermissionSettingProvider, (final previous, final current) {
    //   if (current) {
    //     initHomePage(ref.context!, isRefresh: true);
    //   }
    // });

    // 检查版本更新
    ref
        .watch(terminalProvider.notifier)
        .fetchTerminalData(type: 1)
        .whenComplete(() async {
      String name = ref.read(terminalProvider).value?.name ?? '';
      print('name-->$name');
      String serviceVersionCode =
          ref.read(terminalProvider).value?.version ?? '';
      String fileUrl = ref.read(terminalProvider).value?.url ?? '';
      int forceUpdate = ref.read(terminalProvider).value?.forceUpdate ?? 1;
      checkVersionCode(ref.context, serviceVersionCode, fileUrl, forceUpdate);
    }).onError((final Object error, final StackTrace stackTrace) {
      print('terminal error: $error');
      print('terminal stackTrace: $stackTrace');
    });

    return HomeState();
  }

  /// 网络重连后重新加载数据
  Future<void> _reloadDataAfterNetworkReconnect() async {
    log("网络恢复连接，重新加载首页数据");
    // 网络恢复时主动刷新位置数据
    await LocationUtil.refreshLocation();
    if (state.pageNum > 0 && !_isLoadingRestaurantList) {
      // 如果已经加载过页面，则刷新当前数据
      _loadSelectedTagData(context: null);
    }
  }

  /// 检查网络状态
  Future<bool> _checkNetworkConnection() async {
    final isConnected = ref.read(connectionStateProvider);
    if (!isConnected) {
      log("网络连接不可用，暂停请求");
      return false;
    }
    return true;
  }

  /// 初始化首页数据 - 按照指定流程重构
  Future<void> initHomePage(final BuildContext context,
      {final bool isRefresh = false}) async {
    // 设置初始状态
    state = HomeState(
      isLoading: true,
      isLocationLoading: true,
      isRestaurantLoading: true,
      isInitialLoading: true,
    );

    try {
      log("开始初始化首页数据");
      ref.read(isOpenPermissionSettingProvider.notifier).state = false;
      
      // 1. 获取定位信息
      log("开始获取位置数据");
      // 使用单例获取位置数据，不需要每次都初始化
      if (isRefresh && Platform.isAndroid) {
        LocationUtil.stopLocation();
        await LocationUtil.getInstance(context: context);
      }

      if (Platform.isIOS) {
        await LocationUtil.getInstance(context: context);
      }
      // 优先获取最新的位置数据
      final locationData = isRefresh
          ? await LocationUtil.getLocationOncePlatform()
          : LocationUtil.getLatestLocationData() ??
              await LocationUtil.getLocationOncePlatform();

      if (locationData != null) {
        log("成功获取位置数据: ${locationData.toString()}");
        LocationModel? gpsLocation;
        // 构建位置模型
        if (locationData['errorCode'] == null) {
          gpsLocation = LocationModel(
            latitude: locationData['latitude'].toString(),
            longitude: locationData['longitude'].toString(),
            accuracy: locationData['accuracy'].toString(),
            speed: locationData['speed'].toString(),
            description: locationData['description'].toString(),
            errorCode: 0,
            address: (locationData['address'].toString().isNotEmpty == true &&
                    locationData['address'].toString().contains('新疆维吾尔自治区'))
                ? locationData['address'].toString().substring(8)
                : locationData['address'].toString(),
          );

          ref.read(locationInfoProvider.notifier).state = gpsLocation;
          // 位置加载完成，更新状态
          state = state.copyWith(
              locationInfo: gpsLocation, isLocationLoading: false);
        } else {
          final errorCode = locationData['errorCode'];
          final isPermissionError =  errorCode != null; // 定位权限错误
          state = state.copyWith(
            isPermissionError: isPermissionError,
            isRestaurantLoading: false,
            isInitialLoading: false,
          );
          return;
        }

        // 2. 检查网络状态并获取location信息
        if (await _checkNetworkConnection()) {
          log("正在获取位置相关信息");
          await _loadHomeData(
            buildingId: null,
            areaId: null,
            lng: gpsLocation?.longitude,
            lat: gpsLocation?.latitude,
          );

        } else {
          state = state.copyWith(
            isRestaurantLoading: false,
            isInitialLoading: false,
            error: S.current.net_work_error,
          );
        }
      } else {
        log("位置数据获取失败或为空: ${locationData?.toString() ?? 'null'}");
        // 位置获取失败
        final errorCode = locationData?['errorCode'];
        final isPermissionError = errorCode != null; // 定位权限错误

        state = state.copyWith(
          isLocationLoading: false,
          isRestaurantLoading: false,
          isInitialLoading: false,
          isPermissionError: isPermissionError,
          error: S.current.location_info_not_found,
        );
      }

      // 全局加载完成
      state = state.copyWith(isLoading: false);
      log("首页初始化完成");
    } catch (e, stack) {
      log("初始化首页出错: $e");
      log("错误堆栈: $stack");
      state = state.copyWith(
        isLoading: false,
        isLocationLoading: false,
        isRestaurantLoading: false,
        isInitialLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载首页数据
  Future<void> _loadHomeData({
    required final int? buildingId,
    required final int? areaId,
    final String? lng,
    final String? lat,
  }) async {
    if (!await _checkNetworkConnection()) return;

    try {
      final homeService = ref.read(homeServiceProvider);

      // 重置页码
      state = state.copyWith(pageNum: 1);

      final homeData = await homeService.fetchHomeData(
        tagIds: state.selectedTag,
        page: state.pageNum,
        buildingId: buildingId,
        areaId: areaId,
        lng: state.locationInfo?.longitude,
        lat: state.locationInfo?.latitude,
      );
      if (homeData.data?.location == null) {
        state = state.copyWith(
          isRestaurantLoading: false,
          isInitialLoading: false,
          isNoServiceAgent: true,
        );
        return;
      }
      // 使用位置数据更新通知数据
      await ref.read(homeNoticeProvider.notifier).fetchHomeData(
            lng: lng,
            lat: lat,
            buildingId: homeData.data?.location?.id,
            areaId: homeData.data?.location?.areaId,
          );

      // 获取位置后的数据
      final noticeData = ref.read(homeNoticeProvider).value;
      final location = noticeData?.location;

      final storageService = ref.read(storageServiceProvider);
      await storageService.write("buildingName", location?.buildingName ?? '');
      await storageService.write(
          "buildingNameZh", location?.buildingNameZh ?? '');
      await storageService.write("buildingId", (location?.id ?? 0).toString());
      await storageService.write("areaId", (location?.areaId ?? 0).toString());
      await storageService.write("areaName", location?.areaName ?? '');
      //3. 只有在获取到areaId后才加载首页信息
      final locationAreaId = location?.areaId ?? 0;
      if (locationAreaId > 0) {
        log("已获取到areaId: $locationAreaId，开始加载首页数据");
      } else {
        // log("未获取到有效的areaId，无法加载首页数据");
        // state = state.copyWith(
        //   isRestaurantLoading: false,
        //   isInitialLoading: false,
        //   error: S.current.area_info_not_found,
        // );
      }

      // 更新状态
      final canLoadMore = homeService.checkCanLoadMore(homeData.data);
      final restaurantList = homeData.data?.restaurantList?.items ?? [];

      // 餐厅列表加载完成
      state = state.copyWith(
        homeData: homeData.data,
        restaurantList: restaurantList,
        canAddRestaurantData: canLoadMore,
        isRestaurantLoading: false,
        isInitialLoading: false,
        hasHomeInfo: true,
        error: homeData.success ? null : homeData.msg,
      );

      // 显示推荐弹窗
      log("餐厅列表加载完成，数量: ${restaurantList.length}");
    } catch (e) {
      log("加载餐厅列表失败: $e");
      state = state.copyWith(
        isRestaurantLoading: false,
        isInitialLoading: false,
        error: e.toString(),
      );
    } finally {
      _isLoadingRestaurantList = false;
    }
  }

  Future<void> checkVersionCode(
    final context,
    final String serviceVersionCode,
    final String url,
    final int forceUpdate,
  ) async {
    // 获取当前版本号
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String currentVersionCode = packageInfo.version;
    print('currentVersionCode:$currentVersionCode');
    print('serviceVersionCode:$serviceVersionCode');
    int currentVersion = int.parse(currentVersionCode.split('.').join(''));
    int serviceVersion = int.parse(serviceVersionCode.split('.').join(''));
    await StorageService().write('currentVersion', currentVersion.toString());
    await StorageService().write('serviceVersion', serviceVersion.toString());
    // 版本比较
    if (serviceVersion > currentVersion) {
      _loadUpgradeDialog(context, url, forceUpdate);
    }
  }

  /// 获取当前位置信息
  Map<String, dynamic> getCurrentLocation() {
    // final noticeData = ref.read(homeNoticeProvider).value;
    final location = state.homeData?.location;

    return {
      'latitude': (location?.lat ?? 0).toString(),
      'longitude': (location?.lng ?? 0).toString(),
      'buildingName': location?.buildingName ?? '',
      'buildingNameZh': location?.buildingNameZh ?? '',
      'areaName': location?.areaName ?? '',
      'buildingId': location?.id ?? 0,
      'areaId': location?.areaId ?? 0,
    };
  }

  /// 更新位置后更新首页数据
  Future<void> updateLocationData(
    final int buildingId,
    final int areaId, {
    required final BuildContext context,
  }) async {
    if (_isLoadingRestaurantList) return;
    if (!await _checkNetworkConnection()) return;
    if (areaId <= 0) {
      log("areaId无效，无法更新数据");
      return;
    }
    // 滚动到顶部
    final scrollController = ref.read(homeScrollControllerProvider);
    if (scrollController != null && scrollController.hasClients) {
      await scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
    // 重置页码和选中标签
    state = state.copyWith(selectedTag: '1', pageNum: 1);

    _isLoadingRestaurantList = true;
    try {
      final homeService = ref.read(homeServiceProvider);

      // 加载新地址的数据
      final newHomeData = await homeService.fetchHomeData(
        tagIds: '1',
        page: 1,
        buildingId: buildingId,
        areaId: areaId,
      );

      // 更新状态
      final canLoadMore = homeService.checkCanLoadMore(newHomeData.data);
      final restaurantList = newHomeData.data?.restaurantList?.items ?? [];

      // 更新控制器状态
      state = state.copyWith(
        isNoServiceAgent: newHomeData.data?.location == null,
        homeData: newHomeData.data,
        restaurantList: restaurantList,
        canAddRestaurantData: canLoadMore,
      );

      // 更新通知数据 - 注意这个操作不会影响到推荐弹窗的显示
      await ref
          .read(homeNoticeProvider.notifier)
          .fetchHomeData(buildingId: buildingId, areaId: areaId);
    } finally {
      _isLoadingRestaurantList = false;
    }
  }

  /// 下拉刷新首页数据
  Future<void> refreshHomeData({required final BuildContext context}) async {
    if (_isLoadingRestaurantList) return;
    if (!await _checkNetworkConnection()) {
      state = state.copyWith(
        isRestaurantLoading: false,
        isInitialLoading: false,
      );
      return;
    }

    // 下拉刷新时主动刷新位置数据
    await LocationUtil.refreshLocation();

    // 获取位置信息
    final locationInfo = getCurrentLocation();
    final int buildingId = locationInfo['buildingId'];
    final int areaId = locationInfo['areaId'];

    // if (areaId <= 0) {
    //   log("areaId无效，无法刷新数据");
    //   return;
    // }

    _isLoadingRestaurantList = true;
    state = state.copyWith(
      isRestaurantLoading: true,
      isInitialLoading: false, // 刷新时不显示骨架屏
    );

    try {
      log("下拉刷新首页数据");
      // 加载首页数据但不显示推荐弹窗 - 下拉刷新不应该显示推荐弹窗
      await _loadHomeData(buildingId: buildingId, areaId: areaId);
    } catch (e) {
      log("下拉刷新失败: $e");
      state = state.copyWith(isRestaurantLoading: false);
    } finally {
      _isLoadingRestaurantList = false;
    }
  }

  /// 处理滚动到底部加载更多
  Future<void> handleScrollToBottom() async {
    if (_isLoadingRestaurantList || !state.canAddRestaurantData) return;
    if (!await _checkNetworkConnection()) return;

    // 获取位置信息
    final locationInfo = getCurrentLocation();
    final int buildingId = locationInfo['buildingId'];
    final int areaId = locationInfo['areaId'];

    if (areaId <= 0) {
      log("areaId无效，无法加载更多数据");
      return;
    }

    // 设置加载更多状态
    state = state.copyWith(isLoadingMore: true);

    // 增加页码
    final newPageNum = state.pageNum + 1;
    state = state.copyWith(pageNum: newPageNum);

    log("加载更多: 页码=${state.pageNum}, buildingId=$buildingId, areaId=$areaId");

    _isLoadingRestaurantList = true;
    try {
      final homeService = ref.read(homeServiceProvider);

      final newHomeData = await homeService.fetchHomeData(
        tagIds: state.selectedTag,
        page: state.pageNum,
        buildingId: buildingId,
        areaId: areaId,
        lat: state.locationInfo?.latitude,
        lng: state.locationInfo?.longitude,
      );

      // 合并数据
      final mergedHomeData = homeService.mergeRestaurantList(
        state.homeData,
        newHomeData.data,
        state.pageNum,
      );

      // 检查是否可以加载更多
      final canLoadMore = homeService.checkCanLoadMore(newHomeData.data);

      // 更新餐厅列表
      List<RestaurantItems> updatedList = [...state.restaurantList];
      if (newHomeData.data?.restaurantList?.items != null) {
        updatedList.addAll(newHomeData.data!.restaurantList!.items ?? []);
      }

      // 更新状态
      state = state.copyWith(
        homeData: mergedHomeData,
        restaurantList: updatedList,
        canAddRestaurantData: canLoadMore,
        isLoadingMore: false,
      );
    } catch (e) {
      // 处理错误
      state = state.copyWith(isLoadingMore: false);
    } finally {
      _isLoadingRestaurantList = false;
    }
  }

  /// 更新选中的标签
  Future<void> updateSelectedTag(
    final String tagId, {
    required final BuildContext context,
  }) async {
    if (state.selectedTag == tagId) return;

    state = state.copyWith(
      selectedTag: tagId, pageNum: 1, // 重置页码
    );

    // 加载新标签的数据
    await _loadSelectedTagData(context: context);
  }

  /// 加载选中标签的数据
  Future<void> _loadSelectedTagData({
    required final BuildContext? context,
  }) async {
    if (_isLoadingRestaurantList) return;
    if (!await _checkNetworkConnection()) return;

    // 获取位置信息
    final locationInfo = getCurrentLocation();
    final int buildingId = locationInfo['buildingId'];
    final int areaId = locationInfo['areaId'];

    if (areaId <= 0) {
      log("areaId无效，无法加载标签数据");
      return;
    }

    _isLoadingRestaurantList = true;
    try {
      final homeService = ref.read(homeServiceProvider);

      final homeData = await homeService.fetchHomeData(
        tagIds: state.selectedTag,
        page: state.pageNum,
        buildingId: buildingId,
        areaId: areaId,
        lat: state.locationInfo?.latitude,
        lng: state.locationInfo?.longitude,
      );

      // 更新状态
      final canLoadMore = homeService.checkCanLoadMore(homeData.data);
      final restaurantList = homeData.data?.restaurantList?.items ?? [];

      state = state.copyWith(
        homeData: homeData.data,
        restaurantList: restaurantList,
        canAddRestaurantData: canLoadMore,
      );
    } finally {
      _isLoadingRestaurantList = false;
    }
  }

  /// 切换语言
  Future<void> toggleLanguage(final BuildContext context) async {
    // 确保不会显示加载状态
    state = state.copyWith(
      hasHomeInfo: false,
      isInitialLoading: false,
    );

    String newLanguage = ref.read(languageProvider) == 'ug' ? 'zh' : 'ug';
    final storageService = ref.read(localStorageRepositoryProvider);
    log("切换语言: ${ref.read(languageProvider)} -> $newLanguage");

    // 更新本地存储和provider
    storageService.saveLang(newLanguage);
    ref.read(languageProvider.notifier).state = newLanguage;

    // 更新state中的语言
    state = state.copyWith(
      isInitialLoading: false, // 确保不会显示加载状态
    );
  }

  Future<void> _loadRecommendDialog() async {
    HomeNoticeData? noticeData = ref.read(homeNoticeProvider).value;
    log("显示推荐弹窗");
    if ((noticeData?.popupAdver ?? []).isNotEmpty &&
        ref.context != null) {
      await showRecommendDialogTask(
        noticeData?.popupAdver ?? [],
        noticeData?.location?.id ?? 0,
        ref.context!,
      );
    }
    // 加载折扣信息
    if (ref.context != null) {
      _loadDiscountInfo(ref.context!);
    }
  }

  Future<void> loadCouponDialog() async {
    HomeNoticeData? noticeData = ref.read(homeNoticeProvider).value;
    log("显示优惠券弹窗");
    if ((noticeData?.coupon ?? []).isNotEmpty &&
        ref.context != null) {
      await showCouponDialogTask(
        noticeData?.coupon ?? [],
        noticeData?.location?.id ?? 0,
        noticeData?.location?.areaId ?? 0,
        ref.context!,
      );
    }
  }

  /// 加载折扣信息
  Future<void> _loadDiscountInfo(final BuildContext context) async {
    await Future.delayed(const Duration(seconds: 1));
    final noticeData = ref.read(homeNoticeProvider).value;
    final location = noticeData?.location;
    final int buildingId = location?.id ?? 0;
    log("获取折扣信息: ${state.homeData != null ? '成功' : '失败'}");
    if (state.homeData?.discount != null) {
      showDiscountDialogTask(
        state.homeData?.discount,
        buildingId,
        context,
      );
    }
  }

  Future<void> _loadUpgradeDialog(
    final BuildContext context,
    final String fileUrl,
    final int forceUpdate,
  ) async {
      await showUpgradeDialogTask(
        forceUpdate,
        fileUrl,
        context,
      );
  }

  /// 处理选择地址事件
  void handleSelectAddress() {
    // 导航到地址选择页面
    if (ref.context != null) {
      // 获取当前定位
      final locationData = getCurrentLocation();

      String latitude = locationData['latitude'];
      String longitude = locationData['longitude'];
      String buildingName = locationData['buildingName'];
      String buildingNameZh = locationData['buildingNameZh'];
      String areaName = locationData['areaName'];
      int buildingId = locationData['buildingId'];
      int areaId = locationData['areaId'];

      Navigator.of(ref.context!)
          .push(MaterialPageRoute(
        builder: (final context) => AddressPage(
            lat: latitude,
            lng: longitude,
            buildingName: buildingName,
            buildingNameZh: buildingNameZh,
            areaName: areaName,
            buildingId: buildingId,
            areaId: areaId),
      ))
          .then((final value) async {
        if (value == null) return;
        Map<String, dynamic> addressParam = value as Map<String, dynamic>;
        int selectedBuildingId = addressParam['building_id'];
        int selectedAreaId = addressParam['area_id'];

        // 使用HomeController更新地址数据
        await updateLocationData(
          selectedBuildingId,
          selectedAreaId,
          context: ref.context!,
        );
      });
    }
  }

  /// 处理重试事件
  Future<void> handleRetry() async {
    final context = ref.context;
    if (context != null) {
      await initHomePage(context, isRefresh: true);
    }
  }

  /// 处理代理申请事件
  void handleAsAgent() {
    // 导航到代理申请页面
    if (ref.context != null) {
      // 这里需要实现代理申请逻辑
      log("用户点击申请代理");
      ref.context!.push(
        AppPaths.webViewPage,
        extra: {
          'url': UrlConstants.supportUrl,
          'title': S.current.nov_be_proxy,
        },
      );
    }
  }

  /// 是否已经打开权限设置
  bool isOpenPermissionSetting = false;
  
}
